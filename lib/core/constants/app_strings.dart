class AppStrings {
  static const String appName = "مرشد المزارع"; // Placeholder
  static const String dashboardTitle = "لوحة التحكم"; // Placeholder
  static const String consultationsTab = "استشارات"; // Placeholder
  static const String appointmentsTab = "مواعيد"; // Placeholder
  static const String plantMonitoringTab = "مراقبة النبات"; // Placeholder
  static const String reportsTab = "تقارير"; // Placeholder
  static const String quickStatsTitle = "إحصائيات سريعة"; // Placeholder
  static const String statNew = "جديد"; // Placeholder
  static const String statPending = "قيد الانتظار"; // Placeholder
  static const String statUrgent = "عاجل"; // Placeholder
  static const String statCompleted = "مكتمل"; // Placeholder

  // You can add more strings here as needed
  static const String loading = "جارٍ التحميل...";
  static const String error = "حدث خطأ";
  static const String noData = "لا توجد بيانات لعرضها.";
  static const String retry = "إعادة المحاولة";

  // Smart Assistant related strings
  static const String smartAssistantTitle = "المرشد الذكي";
  static const String typeYourMessage = "اكتب رسالتك هنا...";
  static const String sendMessage = "إرسال";
  static const String attachImage = "إرفاق صورة";
  static const String seasonalAdviceTooltip = "نصيحة موسمية";
  static const String humanAdvisorTooltip = "التحدث لمرشد بشري";
  // Titles for screens (assuming they are different from tabs)
  static const String consultationsTitle = "شاشة الاستشارات"; // Placeholder
  static const String appointmentsTitle = "شاشة المواعيد"; // Placeholder
  static const String plantMonitoringTitle = "شاشة مراقبة النبات"; // Placeholder
  static const String reportsTitle = "شاشة التقارير"; // Placeholder

  // Prevent instantiation
  AppStrings._();
}

