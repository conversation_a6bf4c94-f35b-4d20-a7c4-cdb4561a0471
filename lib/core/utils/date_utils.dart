import 'package:intl/intl.dart';

// Placeholder function - replace with actual implementation
String formatTimeAgo(DateTime dateTime) {
  final now = DateTime.now();
  final difference = now.difference(dateTime);

  if (difference.inSeconds < 60) {
    return 'الآن';
  } else if (difference.inMinutes < 60) {
    return '${difference.inMinutes} دقيقة مضت';
  } else if (difference.inHours < 24) {
    return '${difference.inHours} ساعة مضت';
  } else {
    // Using intl package for more robust date formatting if needed
    // Ensure 'intl' is added to pubspec.yaml
    // return DateFormat('yyyy-MM-dd').format(dateTime);
    return '${difference.inDays} يوم مضى'; // Simple fallback
  }
}

