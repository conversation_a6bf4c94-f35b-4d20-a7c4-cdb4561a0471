import 'package:flutter/material.dart';

/// فئة للتحقق من صحة البيانات في النماذج
class FormValidators {
  
  /// التحقق من أن النص غير فارغ
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    return null;
  }

  /// التحقق من نوع المحصول
  static String? validateCropType(String? value) {
    return validateRequired(value, 'نوع المحصول');
  }

  /// التحقق من وصف المشكلة
  static String? validateProblemDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى وصف المشكلة';
    }
    if (value.trim().length < 10) {
      return 'يجب أن يكون الوصف 10 أحرف على الأقل';
    }
    if (value.trim().length > 500) {
      return 'يجب أن يكون الوصف أقل من 500 حرف';
    }
    return null;
  }

  /// التحقق من المساحة
  static String? validateArea(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال المساحة';
    }
    
    final area = double.tryParse(value);
    if (area == null) {
      return 'يرجى إدخال رقم صحيح للمساحة';
    }
    
    if (area <= 0) {
      return 'يجب أن تكون المساحة أكبر من صفر';
    }
    
    if (area > 10000) {
      return 'المساحة كبيرة جداً';
    }
    
    return null;
  }

  /// التحقق من الموقع
  static String? validateLocation(String? value) {
    return validateRequired(value, 'الموقع');
  }

  /// التحقق من أن النموذج صالح
  static bool isFormValid(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  /// عرض رسالة خطأ
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
