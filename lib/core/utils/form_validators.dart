/// فئة للتحقق من صحة البيانات في النماذج
class FormValidators {
  
  /// التحقق من أن النص غير فارغ
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    return null;
  }

  /// التحقق من نوع المحصول
  static String? validateCropType(String? value) {
    return validateRequired(value, 'نوع المحصول');
  }

  /// التحقق من وصف المشكلة
  static String? validateProblemDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى وصف المشكلة';
    }
    if (value.trim().length < 10) {
      return 'يجب أن يكون الوصف 10 أحرف على الأقل';
    }
    if (value.trim().length > 500) {
      return 'يجب أن يكون الوصف أقل من 500 حرف';
    }
    return null;
  }

  /// التحقق من المساحة
  static String? validateArea(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال المساحة';
    }
    
    final area = double.tryParse(value);
    if (area == null) {
      return 'يرجى إدخال رقم صحيح للمساحة';
    }
    
    if (area <= 0) {
      return 'يجب أن تكون المساحة أكبر من صفر';
    }
    
    if (area > 10000) {
      return 'المساحة كبيرة جداً';
    }
    
    return null;
  }

  /// التحقق من الموقع
  static String? validateLocation(String? value) {
    return validateRequired(value, 'الموقع');
  }

  /// التحقق من التاريخ
  static String? validateDate(DateTime? date) {
    if (date == null) {
      return 'يرجى اختيار التاريخ';
    }
    
    final now = DateTime.now();
    if (date.isBefore(now)) {
      return 'لا يمكن اختيار تاريخ في الماضي';
    }
    
    final maxDate = now.add(const Duration(days: 90));
    if (date.isAfter(maxDate)) {
      return 'لا يمكن حجز موعد بعد 3 أشهر';
    }
    
    return null;
  }

  /// التحقق من الوقت
  static String? validateTime(TimeOfDay? time) {
    if (time == null) {
      return 'يرجى اختيار الوقت';
    }
    
    // التحقق من ساعات العمل (8 صباحاً - 6 مساءً)
    if (time.hour < 8 || time.hour >= 18) {
      return 'ساعات العمل من 8 صباحاً إلى 6 مساءً';
    }
    
    return null;
  }

  /// التحقق من موضوع الموعد
  static String? validateAppointmentTopic(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال موضوع الموعد';
    }
    if (value.trim().length < 5) {
      return 'يجب أن يكون الموضوع 5 أحرف على الأقل';
    }
    return null;
  }

  /// التحقق من اسم النبات
  static String? validatePlantName(String? value) {
    return validateRequired(value, 'اسم النبات');
  }

  /// التحقق من الأعراض
  static String? validateSymptoms(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى وصف الأعراض';
    }
    if (value.trim().length < 5) {
      return 'يجب أن يكون وصف الأعراض 5 أحرف على الأقل';
    }
    return null;
  }

  /// التحقق من رقم الهاتف
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }
    
    // إزالة المسافات والرموز
    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    // التحقق من الطول والتنسيق
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return 'رقم الهاتف غير صحيح';
    }
    
    return null;
  }

  /// التحقق من البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  /// التحقق من كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    return null;
  }

  /// التحقق من تطابق كلمة المرور
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'يرجى تأكيد كلمة المرور';
    }
    
    if (value != password) {
      return 'كلمة المرور غير متطابقة';
    }
    
    return null;
  }

  /// التحقق من أن النموذج صالح
  static bool isFormValid(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  /// عرض رسالة خطأ
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
