import 'package:intl/intl.dart';

/// أدوات مساعدة لتنسيق البيانات.
class AppFormatters {

  /// تنسيق التاريخ والوقت.
  /// 
  /// [dateTime] التاريخ والوقت المطلوب تنسيقه.
  /// [format] صيغة التنسيق المطلوبة (افتراضي: 'yyyy-MM-dd hh:mm a').
  static String formatDateTime(DateTime dateTime, {String format = 'yyyy-MM-dd hh:mm a'}) {
    // تأكد من إضافة حزمة intl إلى pubspec.yaml
    // تأكد من تهيئة التوطين العربي في main.dart إذا لزم الأمر
    // import 'package:intl/date_symbol_data_local.dart';
    // await initializeDateFormatting('ar');
    try {
      final formatter = DateFormat(format, 'ar'); // استخدام التوطين العربي
      return formatter.format(dateTime);
    } catch (e) {
      print('خطأ في تنسيق التاريخ: $e');
      return dateTime.toIso8601String(); // صيغة احتياطية
    }
  }

  /// تنسيق الأرقام (مثال: إضافة فواصل الآلاف).
  static String formatNumber(num number) {
    try {
      final formatter = NumberFormat('#,##0.##', 'ar'); // تنسيق عربي
      return formatter.format(number);
    } catch (e) {
      print('خطأ في تنسيق الرقم: $e');
      return number.toString(); // قيمة احتياطية
    }
  }

  // يمكنك إضافة وظائف تنسيق أخرى حسب الحاجة (مثل العملات، النسب المئوية، الخ)
}

