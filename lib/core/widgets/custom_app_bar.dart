import 'package:flutter/material.dart';

/// ويدجت لشريط تطبيق مخصص (مثال أساسي).
/// يمكنك إضافة المزيد من الخصائص والتخصيصات حسب الحاجة.
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final PreferredSizeWidget? bottom;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      leading: leading,
      actions: actions,
      bottom: bottom,
      // يمكنك إضافة المزيد من التخصيصات هنا مثل لون الخلفية، الارتفاع، الخ.
      // backgroundColor: AppColors.primary,
      // elevation: 4,
    );
  }

  /// تحديد الارتفاع المفضل لشريط التطبيق.
  /// يأخذ في الاعتبار ارتفاع شريط التبويبات السفلي (bottom) إذا كان موجودًا.
  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );
}

