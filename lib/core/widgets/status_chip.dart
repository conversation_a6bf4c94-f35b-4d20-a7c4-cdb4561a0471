import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/colors.dart'; // لاستخدام الألوان المعرفة

/// ويدجت لعرض شريحة حالة ملونة (مثل: جديد، معلق، عاجل).
class StatusChip extends StatelessWidget {
  final String statusText;
  final Color backgroundColor;
  final Color textColor;

  const StatusChip({
    super.key,
    required this.statusText,
    required this.backgroundColor,
    this.textColor = Colors.white, // اللون الافتراضي للنص هو الأبيض
  });

  /// بناء الويدجت بناءً على الحالة النصية (مثال).
  /// يمكنك تعديل هذه الدالة لتحديد الألوان تلقائيًا بناءً على النص.
  factory StatusChip.fromString(String status) {
    Color bgColor;
    switch (status.toLowerCase()) {
      case 'جديد':
      case 'new':
        bgColor = AppColors.warning; // أصفر/برتقالي
        break;
      case 'معلق':
      case 'pending':
        bgColor = Colors.blue; // أزرق
        break;
      case 'عاجل':
      case 'urgent':
        bgColor = AppColors.urgent; // أحمر
        break;
      case 'مكتمل':
      case 'completed':
        bgColor = AppColors.success; // أخضر
        break;
      default:
        bgColor = Colors.grey; // رمادي للحالات غير المعروفة
    }
    return StatusChip(
      statusText: status,
      backgroundColor: bgColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(
        statusText,
        style: TextStyle(color: textColor, fontSize: 12),
      ),
      backgroundColor: backgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // لتقليل المساحة حول الشريحة
      labelPadding: EdgeInsets.zero, // إزالة الحشو الداخلي الافتراضي إذا لزم الأمر
      visualDensity: VisualDensity.compact, // لجعلها أصغر
    );
  }
}

