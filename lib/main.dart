import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";
import "package:firebase_core/firebase_core.dart";
import "package:flutter_localizations/flutter_localizations.dart"; // لدعم اللغة العربية

import "bloc/advisor_cubit.dart";
import "core/constants/app_strings.dart";
import "data/repositories/advisor_repository.dart";
import "presentation/main_navigation_screen.dart";
import "presentation/smart_assistant/cubit/smart_assistant_cubit.dart";

// --- نقطة الدخول الرئيسية للتطبيق ---
void main() async {
  // التأكد من تهيئة Flutter Widgets Binding
  WidgetsFlutterBinding.ensureInitialized();

  // *** إصلاح خطأ Firebase: تهيئة Firebase قبل تشغيل التطبيق ***
  try {
    await Firebase.initializeApp();
  } catch (e) {
    // يمكنك التعامل مع الخطأ هنا إذا لزم الأمر
    // على سبيل المثال، عرض رسالة خطأ أو تسجيله
    print("********* خطأ في تهيئة Firebase: $e *********");
    // قد ترغب في عدم تشغيل التطبيق إذا فشلت التهيئة
    // return;
  }

  runApp(const AgriculturalAdvisorApp());
}

// --- الويدجت الرئيسي للتطبيق ---
class AgriculturalAdvisorApp extends StatelessWidget {
  const AgriculturalAdvisorApp({super.key});

  @override
  Widget build(BuildContext context) {
    // استخدام MultiBlocProvider لتوفير الـ Cubits للشجرة بأكملها
    return MultiBlocProvider(
      providers: [
        // توفير AdvisorCubit المسؤول عن بيانات التبويبات الرئيسية
        BlocProvider<AdvisorCubit>(
          // *** إصلاح خطأ الاستدعاء: تم تعديل AdvisorCubit ليأخذ AdvisorRepository ***
          create: (context) => AdvisorCubit(AdvisorRepository()),
        ),
        // توفير SmartAssistantCubit المسؤول عن شاشة المرشد الذكي
        // BlocProvider<SmartAssistantCubit>(
        //   create: (context) => SmartAssistantCubit(),
        // ),
        // يمكنك إضافة المزيد من الـ Cubits هنا إذا احتجت
      ],
      child: MaterialApp(
        title: AppStrings.appName, // استخدام اسم التطبيق من الثوابت
        theme: ThemeData(
          primarySwatch: Colors.green, // اللون الأساسي للتطبيق
          visualDensity: VisualDensity.adaptivePlatformDensity,
          fontFamily: "Cairo", // تحديد الخط العربي الافتراضي (تأكد من إضافته)
          appBarTheme: const AppBarTheme(
            elevation: 1, // ظل خفيف لشريط التطبيق
            centerTitle: true,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black87,
            titleTextStyle: TextStyle(
              fontFamily: "Cairo",
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          bottomNavigationBarTheme: const BottomNavigationBarThemeData(
            selectedItemColor: Colors.green,
            unselectedItemColor: Colors.grey,
            selectedLabelStyle: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        // دعم اللغة العربية
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale("ar", ""), // اللغة العربية
          // يمكنك إضافة لغات أخرى هنا
        ],
        locale: const Locale("ar", ""), // تحديد اللغة العربية كلغة افتراضية
        debugShowCheckedModeBanner: false, // إخفاء شريط Debug
        home: const MainNavigationScreen(), // الشاشة الرئيسية التي تحتوي على شريط التنقل
      ),
    );
  }
}


