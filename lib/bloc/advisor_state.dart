import "package:equatable/equatable.dart";
import "../data/models/consultation.dart"; // <-- تم التعديل: مسار نسبي
import "../data/models/appointment.dart"; // <-- تم التعديل: مسار نسبي
import "../data/models/plant_monitoring.dart"; // <-- تم التعديل: مسار نسبي

/// الحالة الأساسية المجردة لـ AdvisorCubit.
/// نستخدم Equatable لتسهيل مقارنة الحالات.
abstract class AdvisorState extends Equatable {
  const AdvisorState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية للـ Cubit قبل بدء أي عملية.
class AdvisorInitial extends AdvisorState {}

/// حالة جاري التحميل، تشير إلى أن عملية جلب البيانات قيد التنفيذ.
class AdvisorLoading extends AdvisorState {}

/// حالة تم التحميل بنجاح، تحتوي على البيانات التي تم جلبها.
class AdvisorLoaded extends AdvisorState {
  final List<ConsultationModel> consultations;
  final List<AppointmentModel> appointments;
  // *** تم التعديل: تغيير الاسم ليتوافق مع الاستخدام في الواجهات ***
  final List<PlantMonitoringModel> plantMonitorings;

  const AdvisorLoaded({
    this.consultations = const [], // قيم افتراضية فارغة
    this.appointments = const [],
    this.plantMonitorings = const [],
  });

  @override
  List<Object?> get props => [consultations, appointments, plantMonitorings];

  // دالة copyWith لتحديث أجزاء من الحالة بسهولة دون تغيير الباقي
  AdvisorLoaded copyWith({
    List<ConsultationModel>? consultations,
    List<AppointmentModel>? appointments,
    List<PlantMonitoringModel>? plantMonitorings,
  }) {
    return AdvisorLoaded(
      consultations: consultations ?? this.consultations,
      appointments: appointments ?? this.appointments,
      plantMonitorings: plantMonitorings ?? this.plantMonitorings,
    );
  }
}

/// حالة حدوث خطأ أثناء جلب البيانات.
/// تحتوي على رسالة الخطأ للمستخدم.
class AdvisorError extends AdvisorState {
  final String message;

  const AdvisorError(this.message);

  @override
  List<Object?> get props => [message];
}

//