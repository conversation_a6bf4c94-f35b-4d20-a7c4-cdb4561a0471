import "package:flutter_bloc/flutter_bloc.dart";
import "../data/models/appointment.dart";
import "../data/models/consultation.dart";
import "../data/models/plant_monitoring.dart";
import "../data/repositories/advisor_repository.dart"; // <-- تم التعديل: مسار نسبي
import "advisor_state.dart"; // <-- تم التعديل: مسار نسبي

/// الـ Cubit المسؤول عن إدارة حالة البيانات الرئيسية للتطبيق (الاستشارات، المواعيد، إلخ).
class AdvisorCubit extends Cubit<AdvisorState> {
  final AdvisorRepository _advisorRepository;

  // TODO: الحصول على معرف المرشد الحالي بشكل صحيح (مثل من المصادقة)
  final String _currentAdvisorId = "placeholder_advisor_id";

  // *** تم التعديل: إضافة المستودع كمتطلب في الباني ***
  AdvisorCubit(this._advisorRepository) : super(AdvisorInitial()) {
    // *** تم التعديل: استدعاء دالة لجلب جميع البيانات الأولية ***
    fetchAllData();
  }

  /// دالة لجلب جميع البيانات الأولية (استشارات، مواعيد، مراقبة).
  Future<void> fetchAllData() async {
    emit(AdvisorLoading());
    try {
      // جلب جميع البيانات بالتوازي لتحسين الأداء
      final results = await Future.wait([
        _advisorRepository.getConsultations(_currentAdvisorId),
        _advisorRepository.getAppointments(_currentAdvisorId),
        _advisorRepository.getMonitoredPlants(_currentAdvisorId), // <-- تم تصحيح اسم الدالة وتمرير المعرف
      ]);

      // التأكد من أن النتائج هي من الأنواع المتوقعة
      final consultations = results[0] as List<ConsultationModel>;
      final appointments = results[1] as List<AppointmentModel>;
      final plantMonitorings = results[2] as List<PlantMonitoringModel>;

      emit(AdvisorLoaded(
        consultations: consultations,
        appointments: appointments,
        plantMonitorings: plantMonitorings,
      ));
    } catch (e) {
      print("خطأ في AdvisorCubit عند جلب البيانات: $e");
      emit(AdvisorError("حدث خطأ أثناء تحميل البيانات: ${e.toString()}"));
    }
  }

  // --- دوال منفصلة لجلب كل نوع بيانات (يمكن استدعاؤها للتحديث) ---

  /// دالة لجلب الاستشارات فقط.
  Future<void> fetchConsultations() async {
    // الحفاظ على الحالة الحالية للبيانات الأخرى أثناء التحميل
    final currentState = state;
    List<AppointmentModel> currentAppointments = [];
    List<PlantMonitoringModel> currentPlantMonitorings = [];
    if (currentState is AdvisorLoaded) {
      currentAppointments = currentState.appointments;
      currentPlantMonitorings = currentState.plantMonitorings;
    }
    emit(AdvisorLoading()); // يمكن تخصيص حالة تحميل جزئي إذا أردت

    try {
      final consultations = await _advisorRepository.getConsultations(_currentAdvisorId);
      emit(AdvisorLoaded(
        consultations: consultations,
        appointments: currentAppointments, // استخدام البيانات القديمة
        plantMonitorings: currentPlantMonitorings, // استخدام البيانات القديمة
      ));
    } catch (e) {
      print("خطأ في AdvisorCubit عند جلب الاستشارات: $e");
      // العودة للحالة السابقة مع رسالة خطأ
      emit(AdvisorError("حدث خطأ أثناء تحميل الاستشارات: ${e.toString()}"));
      // أو إعادة إصدار الحالة المحملة السابقة إذا كانت موجودة
      // if (currentState is AdvisorLoaded) emit(currentState);
    }
  }

  /// دالة لجلب المواعيد فقط (مع فلتر اختياري).
  Future<void> fetchAppointments({String? filter}) async {
    final currentState = state;
    List<ConsultationModel> currentConsultations = [];
    List<PlantMonitoringModel> currentPlantMonitorings = [];
    if (currentState is AdvisorLoaded) {
      currentConsultations = currentState.consultations;
      currentPlantMonitorings = currentState.plantMonitorings;
    }
    emit(AdvisorLoading());

    try {
      // TODO: تطبيق الفلتر في المستودع أو هنا
      final appointments = await _advisorRepository.getAppointments(_currentAdvisorId);
      emit(AdvisorLoaded(
        consultations: currentConsultations,
        appointments: appointments,
        plantMonitorings: currentPlantMonitorings,
      ));
    } catch (e) {
      print("خطأ في AdvisorCubit عند جلب المواعيد: $e");
      emit(AdvisorError("حدث خطأ أثناء تحميل المواعيد: ${e.toString()}"));
    }
  }

  /// دالة لجلب بيانات مراقبة النباتات فقط.
  Future<void> fetchPlantMonitorings() async {
    final currentState = state;
    List<ConsultationModel> currentConsultations = [];
    List<AppointmentModel> currentAppointments = [];
    if (currentState is AdvisorLoaded) {
      currentConsultations = currentState.consultations;
      currentAppointments = currentState.appointments;
    }
    emit(AdvisorLoading());

    try {
      final plantMonitorings = await _advisorRepository.getMonitoredPlants(_currentAdvisorId); // <-- تم تصحيح اسم الدالة وتمرير المعرف
      emit(AdvisorLoaded(
        consultations: currentConsultations,
        appointments: currentAppointments,
        plantMonitorings: plantMonitorings,
      ));
    } catch (e) {
      print("خطأ في AdvisorCubit عند جلب بيانات المراقبة: $e");
      emit(AdvisorError("حدث خطأ أثناء تحميل بيانات المراقبة: ${e.toString()}"));
    }
  }

  // يمكنك إضافة دوال أخرى هنا لتحديث البيانات أو إضافتها أو حذفها
}


