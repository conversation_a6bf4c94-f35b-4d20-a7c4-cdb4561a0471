import 'package:agriculture/data/datasources/firebase_datasource.dart';
import 'package:agriculture/data/models/consultation.dart';
import 'package:agriculture/data/models/appointment.dart';
import 'package:agriculture/data/models/plant_monitoring.dart';

/// مستودع البيانات الخاص بالمرشد.
/// يعمل كوسيط بين طبقة الـ BLoC/Cubit ومصادر البيانات (حالياً Firebase).
/// يوفر واجهة موحدة لجلب البيانات المتعلقة بالمرشد.
class AdvisorRepository {
  // الاعتماد على مصدر بيانات Firebase
  // يمكن حقن هذا الاعتماد (Dependency Injection) في تطبيق حقيقي
  final FirebaseDatasource _firebaseDatasource = FirebaseDatasource();

  /// جلب قائمة الاستشارات لمرشد معين.
  ///
  /// [advisorId] معرف المرشد.
  /// يقوم باستدعاء الوظيفة المقابلة في مصدر البيانات.
  Future<List<ConsultationModel>> getConsultations(String advisorId) async {
    try {
      // استدعاء مصدر البيانات لجلب الاستشارات
      return await _firebaseDatasource.getConsultations(advisorId);
    } catch (e) {
      // يمكن التعامل مع الخطأ هنا أو إعادة رميه لطبقة أعلى (Cubit)
      print('خطأ في المستودع عند جلب الاستشارات: $e');
      // إعادة رمي الخطأ للسماح للـ Cubit بمعالجته وعرض رسالة للمستخدم
      rethrow;
    }
  }

  /// جلب قائمة المواعيد لمرشد معين.
  ///
  /// [advisorId] معرف المرشد.
  Future<List<AppointmentModel>> getAppointments(String advisorId) async {
    try {
      // استدعاء مصدر البيانات لجلب المواعيد
      return await _firebaseDatasource.getAppointments(advisorId);
    } catch (e) {
      print('خطأ في المستودع عند جلب المواعيد: $e');
      rethrow;
    }
  }

  /// جلب قائمة النباتات المراقبة لمرشد معين.
  ///
  /// [advisorId] معرف المرشد.
  Future<List<PlantMonitoringModel>> getMonitoredPlants(String advisorId) async {
    try {
      // استدعاء مصدر البيانات لجلب النباتات المراقبة
      return await _firebaseDatasource.getMonitoredPlants(advisorId);
    } catch (e) {
      print('خطأ في المستودع عند جلب النباتات المراقبة: $e');
      rethrow;
    }
  }

  // --- وظائف إضافية (يمكن إضافتها لاحقًا) ---

  /// إضافة استشارة جديدة (مثال)
  Future<void> addConsultation(ConsultationModel consultation) async {
    try {
      await _firebaseDatasource.addConsultation(consultation);
    } catch (e) {
      print('خطأ في المستودع عند إضافة استشارة: $e');
      rethrow;
    }
  }

  /// تحديث حالة استشارة (مثال)
  Future<void> updateConsultationStatus(String consultationId, String newStatus) async {
    try {
      await _firebaseDatasource.updateConsultationStatus(consultationId, newStatus);
    } catch (e) {
      print('خطأ في المستودع عند تحديث حالة الاستشارة: $e');
      rethrow;
    }
  }

  // يمكنك إضافة وظائف مشابهة لاستدعاء وظائف إضافة/تحديث/حذف المواعيد والنباتات
}

