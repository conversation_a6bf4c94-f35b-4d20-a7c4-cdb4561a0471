import 'package:cloud_firestore/cloud_firestore.dart';

class AppointmentModel {
  final String id;
  final String userId;
  final String userName;
  final String advisorId;
  final String type; // e.g., 'video_call', 'field_visit', 'online_meeting'
  final String topic;
  final String location;
  final DateTime dateTime;
  final int durationMinutes;
  final String status; // e.g., 'scheduled', 'completed', 'cancelled', 'pending_confirmation'

  AppointmentModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.advisorId,
    required this.type,
    required this.topic,
    required this.location,
    required this.dateTime,
    required this.durationMinutes,
    required this.status,
  });

  // Placeholder for Firebase conversion - adapt fields as needed
  factory AppointmentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AppointmentModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      advisorId: data['advisorId'] ?? '',
      type: data['type'] ?? 'online_meeting',
      topic: data['topic'] ?? 'استشارة عامة',
      location: data['location'] ?? 'أونلاين',
      dateTime: (data['dateTime'] is Timestamp)
          ? (data['dateTime'] as Timestamp).toDate()
          : DateTime.now(),
      durationMinutes: data['durationMinutes'] ?? 30,
      status: data['status'] ?? 'scheduled',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'advisorId': advisorId,
      'type': type,
      'topic': topic,
      'location': location,
      'dateTime': Timestamp.fromDate(dateTime),
      'durationMinutes': durationMinutes,
      'status': status,
    };
  }
}

