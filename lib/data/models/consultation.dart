import 'package:cloud_firestore/cloud_firestore.dart'; // Ensure cloud_firestore is in pubspec.yaml

class ConsultationModel {
  final String id;
  final String userId;
  final String userName;
  final String advisorId;
  final String cropType;
  final String problemDescription;
  final double area;
  final int imagesCount;
  final String location;
  final String status;
  final DateTime createdAt;

  ConsultationModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.advisorId,
    required this.cropType,
    required this.problemDescription,
    required this.area,
    required this.imagesCount,
    required this.location,
    required this.status,
    required this.createdAt,
  });

  // تحويل من/إلى Firebase
  factory ConsultationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ConsultationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      advisorId: data['advisorId'] ?? '',
      cropType: data['cropType'] ?? '',
      problemDescription: data['problemDescription'] ?? '',
      area: (data['area'] ?? 0.0).toDouble(),
      imagesCount: data['imagesCount'] ?? 0,
      location: data['location'] ?? '',
      status: data['status'] ?? 'pending',
      // Handle potential null or incorrect type for Timestamp
      createdAt: (data['createdAt'] is Timestamp)
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(), // Fallback to now if invalid
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'advisorId': advisorId,
      'cropType': cropType,
      'problemDescription': problemDescription,
      'area': area,
      'imagesCount': imagesCount,
      'location': location,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

