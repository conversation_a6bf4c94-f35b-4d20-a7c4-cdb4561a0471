import 'package:cloud_firestore/cloud_firestore.dart';

class PlantMonitoringModel {
  final String id;
  final String plantId; // Unique ID for the specific plant being monitored
  final String plantName; // <-- تمت الإضافة: اسم النبات المحدد
  final String userId;
  final String userName;
  final String advisorId;
  final String cropType;
  final String location;
  final String status; // e.g., 'healthy', 'warning', 'critical', 'needs_attention'
  final String lastSymptoms; // Description of latest observed symptoms
  final DateTime lastUpdate;
  final List<String> imageUrls; // URLs of images related to this plant

  // <-- تمت الإضافة: Getter للحصول على أول صورة أو null
  String? get imageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;

  PlantMonitoringModel({
    required this.id,
    required this.plantId,
    required this.plantName, // <-- تمت الإضافة
    required this.userId,
    required this.userName,
    required this.advisorId,
    required this.cropType,
    required this.location,
    required this.status,
    required this.lastSymptoms,
    required this.lastUpdate,
    this.imageUrls = const [],
  });

  // Placeholder for Firebase conversion - adapt fields as needed
  factory PlantMonitoringModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PlantMonitoringModel(
      id: doc.id,
      plantId: data['plantId'] ?? '',
      plantName: data['plantName'] ?? data['cropType'] ?? '', // <-- تمت الإضافة (مع قيمة احتياطية من cropType)
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      advisorId: data['advisorId'] ?? '',
      cropType: data['cropType'] ?? '',
      location: data['location'] ?? '',
      status: data['status'] ?? 'needs_attention',
      lastSymptoms: data['lastSymptoms'] ?? 'لا توجد أعراض مسجلة',
      lastUpdate: (data['lastUpdate'] is Timestamp)
          ? (data['lastUpdate'] as Timestamp).toDate()
          : DateTime.now(),
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'plantId': plantId,
      'plantName': plantName, // <-- تمت الإضافة
      'userId': userId,
      'userName': userName,
      'advisorId': advisorId,
      'cropType': cropType,
      'location': location,
      'status': status,
      'lastSymptoms': lastSymptoms,
      'lastUpdate': Timestamp.fromDate(lastUpdate),
      'imageUrls': imageUrls,
    };
  }
}

