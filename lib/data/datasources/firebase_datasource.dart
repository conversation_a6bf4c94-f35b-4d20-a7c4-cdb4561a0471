import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:agriculture/data/models/consultation.dart';
import 'package:agriculture/data/models/appointment.dart';
import 'package:agriculture/data/models/plant_monitoring.dart';

/// مصدر البيانات للتفاعل مع Firebase Firestore.
/// يوفر وظائف لجلب البيانات المتعلقة بالاستشارات، المواعيد، ومراقبة النباتات.
class FirebaseDatasource {
  // الحصول على نسخة من Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب قائمة الاستشارات لمرشد معين.
  /// 
  /// [advisorId] معرف المرشد المطلوب جلب استشاراته.
  /// قد تحتاج لإضافة فلاتر إضافية (مثل الحالة) أو ترتيب حسب الحاجة.
  Future<List<ConsultationModel>> getConsultations(String advisorId) async {
    try {
      // الاستعلام عن الاستشارات التي تطابق معرف المرشد
      final querySnapshot = await _firestore
          .collection('consultations') // اسم المجموعة في Firestore
          .where('advisorId', isEqualTo: advisorId)
          // يمكنك إضافة .orderBy('createdAt', descending: true) للترتيب حسب الأحدث
          .get();

      // تحويل المستندات إلى نماذج ConsultationModel
      return querySnapshot.docs
          .map((doc) => ConsultationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      // التعامل مع الأخطاء (مثل طباعة الخطأ أو إعادته)
      print('خطأ في جلب الاستشارات: $e');
      // يمكنك إرجاع قائمة فارغة أو رمي استثناء مخصص
      throw Exception('فشل جلب الاستشارات من Firestore: $e');
    }
  }

  /// جلب قائمة المواعيد لمرشد معين.
  /// 
  /// [advisorId] معرف المرشد المطلوب جلب مواعيده.
  Future<List<AppointmentModel>> getAppointments(String advisorId) async {
    try {
      // الاستعلام عن المواعيد التي تطابق معرف المرشد
      final querySnapshot = await _firestore
          .collection('appointments') // اسم المجموعة في Firestore
          .where('advisorId', isEqualTo: advisorId)
          // يمكنك إضافة .orderBy('dateTime') للترتيب حسب التاريخ
          .get();

      // تحويل المستندات إلى نماذج AppointmentModel
      return querySnapshot.docs
          .map((doc) => AppointmentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('خطأ في جلب المواعيد: $e');
      throw Exception('فشل جلب المواعيد من Firestore: $e');
    }
  }

  /// جلب قائمة النباتات المراقبة لمرشد معين.
  /// 
  /// [advisorId] معرف المرشد المطلوب جلب النباتات المراقبة له.
  Future<List<PlantMonitoringModel>> getMonitoredPlants(String advisorId) async {
    try {
      // الاستعلام عن النباتات المراقبة التي تطابق معرف المرشد
      final querySnapshot = await _firestore
          .collection('plantMonitorings') // اسم المجموعة في Firestore (تأكد من الاسم)
          .where('advisorId', isEqualTo: advisorId)
          // يمكنك إضافة .orderBy('lastUpdate', descending: true) للترتيب
          .get();

      // تحويل المستندات إلى نماذج PlantMonitoringModel
      return querySnapshot.docs
          .map((doc) => PlantMonitoringModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('خطأ في جلب النباتات المراقبة: $e');
      throw Exception('فشل جلب النباتات المراقبة من Firestore: $e');
    }
  }

  // --- وظائف إضافية (يمكن إضافتها لاحقًا) ---

  /// إضافة استشارة جديدة (مثال)
  Future<void> addConsultation(ConsultationModel consultation) async {
    try {
      await _firestore.collection('consultations').add(consultation.toFirestore());
    } catch (e) {
      print('خطأ في إضافة استشارة: $e');
      throw Exception('فشل إضافة استشارة: $e');
    }
  }

  /// تحديث حالة استشارة (مثال)
  Future<void> updateConsultationStatus(String consultationId, String newStatus) async {
    try {
      await _firestore.collection('consultations').doc(consultationId).update({'status': newStatus});
    } catch (e) {
      print('خطأ في تحديث حالة الاستشارة: $e');
      throw Exception('فشل تحديث حالة الاستشارة: $e');
    }
  }

  // يمكنك إضافة وظائف مشابهة لإضافة/تحديث/حذف المواعيد والنباتات المراقبة
}

