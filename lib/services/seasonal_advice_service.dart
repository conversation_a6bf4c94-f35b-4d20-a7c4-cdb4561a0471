import 'package:intl/intl.dart'; // لاستخدام التاريخ

/// خدمة لتقديم نصائح زراعية موسمية.
class SeasonalAdviceService {

  /// الحصول على نصيحة موسمية بناءً على الشهر الحالي.
  /// يمكنك تطوير هذه الوظيفة لاحقًا لتعتمد على الموقع الجغرافي أو بيانات أكثر تفصيلاً.
  String getSeasonalAdvice() {
    // الحصول على الشهر الحالي (من 1 إلى 12)
    final int currentMonth = DateTime.now().month;

    // تحديد النصيحة بناءً على الشهر
    switch (currentMonth) {
      // الربيع (مارس - مايو)
      case 3:
      case 4:
      case 5:
        return "🌱 الربيع: وقت ممتاز لتجهيز التربة وزراعة العديد من الخضروات الصيفية مثل الطماطم، الفلفل، والباذنجان. ابدأ بمكافحة الأعشاب الضارة مبكرًا.";
      // الصيف (يونيو - أغسطس)
      case 6:
      case 7:
      case 8:
        return "☀️ الصيف: احرص على الري المنتظم خاصة في الأيام الحارة. راقب الآفات والأمراض التي تنشط في هذا الموسم مثل المن والعنكبوت الأحمر. ابدأ بحصاد بعض المحاصيل مبكرة النضج.";
      // الخريف (سبتمبر - نوفمبر)
      case 9:
      case 10:
      case 11:
        return "🍂 الخريف: موسم حصاد العديد من المحاصيل الصيفية. ابدأ في زراعة المحاصيل الشتوية مثل البصل، الثوم، والخضروات الورقية. قم بتسميد التربة استعدادًا للشتاء.";
      // الشتاء (ديسمبر - فبراير)
      case 12:
      case 1:
      case 2:
        return "❄️ الشتاء: قم بحماية النباتات الحساسة من الصقيع. قلل من الري لمعظم النباتات. هو وقت مناسب لتقليم الأشجار المتساقطة الأوراق وبعض الشجيرات.";
      default:
        return "لا توجد نصيحة موسمية متاحة حاليًا.";
    }
  }

  /// يمكنك إضافة وظائف أخرى هنا، مثل الحصول على نصائح لمحصول معين أو لمنطقة جغرافية محددة.
}

