import 'package:googleapis_auth/auth_io.dart' as auth;
import 'package:googleapis/dialogflow/v2.dart' as dialogflow;
import 'package:flutter/services.dart' show rootBundle;
import 'dart:convert'; // لاستخدام jsonDecode
// عدلت زيل تعليق
/// خدمة للتفاعل مع Google Dialogflow CX أو ES.
/// تتطلب إعداد بيانات اعتماد الخدمة (Service Account Credentials).
class DialogflowService {
  // معرف المشروع الخاص بك في Google Cloud / Dialogflow
  final String _projectId = "YOUR_DIALOGFLOW_PROJECT_ID"; // !! استبدل بمعرف مشروعك
  // معرف الجلسة الفريد لكل مستخدم أو محادثة
  final String _sessionId = "user-${DateTime.now().millisecondsSinceEpoch}";
  // لغة المحادثة
  final String _languageCode = "ar"; // العربية

  dialogflow.SessionsClient? _sessionsClient;
  bool _isInitialized = false;

  /// تهيئة الخدمة عن طريق تحميل بيانات الاعتماد.
  /// يجب استدعاء هذه الدالة مرة واحدة قبل استخدام الخدمة.
  Future<void> initialize() async {
    if (_isInitialized) return;
    try {
      // 1. تحميل ملف بيانات اعتماد حساب الخدمة (JSON)
      // تأكد من وضع ملف JSON في مجلد assets وإضافته في pubspec.yaml
      final String credentialsJson = await rootBundle.loadString('assets/dialogflow_credentials.json');
      final credentials = auth.ServiceAccountCredentials.fromJson(jsonDecode(credentialsJson));

      // 2. تحديد نطاقات الوصول المطلوبة لـ Dialogflow
      final scopes = [dialogflow.DialogflowApi.cloudPlatformScope];

      // 3. الحصول على عميل HTTP مصادق عليه
      final httpClient = await auth.clientViaServiceAccount(credentials, scopes);

      // 4. إنشاء عميل Dialogflow SessionsClient
      _sessionsClient = dialogflow.SessionsClient(httpClient: httpClient);
      _isInitialized = true;
      print("خدمة Dialogflow تم تهيئتها بنجاح.");

    } catch (e) {
      print("فشل تهيئة خدمة Dialogflow: $e");
      print("تأكد من وجود ملف 'assets/dialogflow_credentials.json' وإضافته إلى pubspec.yaml.");
      print("وتأكد من استبدال 'YOUR_DIALOGFLOW_PROJECT_ID' في dialogflow_service.dart.");
      // يمكنك رمي الخطأ مرة أخرى أو التعامل معه حسب الحاجة
      // throw Exception('Failed to initialize DialogflowService: $e');
    }
  }

  /// إرسال استعلام نصي إلى Dialogflow والحصول على الرد.
  Future<String> sendQuery(String textQuery) async {
    // التأكد من تهيئة الخدمة أولاً
    if (!_isInitialized || _sessionsClient == null) {
      // محاولة التهيئة التلقائية عند أول استدعاء (اختياري)
      await initialize();
      if (!_isInitialized || _sessionsClient == null) {
         return "خطأ: خدمة Dialogflow غير مهيأة. يرجى مراجعة الإعدادات.";
      }
    }

    try {
      // بناء اسم الجلسة
      final sessionName = "projects/$_projectId/agent/sessions/$_sessionId";
      // بناء نص الإدخال
      final textInput = dialogflow.TextInput()
        ..text = textQuery
        ..languageCode = _languageCode;
      // بناء كائن الاستعلام
      final queryInput = dialogflow.QueryInput()..text = textInput;
      // بناء الطلب
      final request = dialogflow.DetectIntentRequest()
        ..session = sessionName
        ..queryInput = queryInput;

      // إرسال الطلب والحصول على الاستجابة
      final response = await _sessionsClient!.detectIntent(request);

      // استخراج نص الرد من الاستجابة
      final fulfillmentText = response.queryResult?.fulfillmentText;

      // إرجاع الرد أو رسالة افتراضية إذا كان الرد فارغًا
      return fulfillmentText ?? "لم أتمكن من فهم ذلك. هل يمكنك إعادة الصياغة؟";

    } catch (e) {
      print("خطأ أثناء إرسال استعلام Dialogflow: $e");
      return "عذرًا، حدث خطأ أثناء الاتصال بالمرشد الذكي.";
    }
  }

  // يمكنك إضافة وظائف أخرى هنا، مثل إرسال الأحداث (Events) أو التعامل مع السياقات (Contexts).
}


// import 'package:googleapis_auth/auth_io.dart' as auth;
// import 'package:flutter/services.dart' show rootBundle;
// import 'dart:convert';

/// خدمة Dialogflow للذكاء الاصطناعي الزراعي
// class DialogflowService {
//   final String _projectId = "agriculture-ai-project"; // استبدل بمعرف مشروعك
//   final String _sessionId = "farm-session-${DateTime.now().millisecondsSinceEpoch}";
//   final String _languageCode = "ar";
//
//   DialogflowGrpcV2Beta1? _dialogflow;
//   bool _isInitialized = false;
//
//   Future<void> initialize() async {
//     if (_isInitialized) return;
//
//     try {
//       final credentialsJson = await rootBundle.loadString('assets/dialogflow_credentials.json');
//       final credentials = auth.ServiceAccountCredentials.fromJson(jsonDecode(credentialsJson));
//       final scopes = ['https://www.googleapis.com/auth/cloud-platform'];
//
//       final httpClient = await auth.clientViaServiceAccount(credentials, scopes);
//
//       _dialogflow = DialogflowGrpcV2Beta1.viaServiceAccount(
//         credentialsJson,
//         _projectId,
//         sessionId: _sessionId,
//         languageCode: _languageCode,
//       );
//
//       _isInitialized = true;
//       print("✅ Dialogflow service initialized successfully");
//     } catch (e) {
//       print("❌ Failed to initialize Dialogflow: $e");
//       throw Exception("فشل تهيئة خدمة الذكاء الاصطناعي");
//     }
//   }
//
//   Future<String> sendQuery(String textQuery) async {
//     if (!_isInitialized || _dialogflow == null) {
//       await initialize();
//       if (!_isInitialized || _dialogflow == null) {
//         return "خطأ: الخدمة غير مهيأة";
//       }
//     }
//
//     try {
//       final response = await _dialogflow!.detectIntent(textQuery);
//       return response.queryResult.fulfillmentText ?? "لم أتمكن من فهم السؤال";
//     } catch (e) {
//       print("Error in sendQuery: $e");
//       return "عذرًا، حدث خطأ أثناء معالجة سؤالك";
//     }
//   }
//
//   /// خاص بالزراعة - تحليل أعراض النبات
//   Future<String> analyzePlantSymptoms(String symptoms, {List<String>? images}) async {
//     if (!_isInitialized || _dialogflow == null) {
//       await initialize();
//     }
//
//     try {
//       final query = """
//       أعراض نباتية:
//       $symptoms
//       ${images != null ? "مع صور مرفقة" : ""}
//       """;
//
//       final response = await _dialogflow!.detectIntent(
//         query,
//         context: "plant-diagnosis-context",
//       );
//
//       return response.queryResult.fulfillmentText ??
//           "لا يمكن تشخيص المشكلة، يرجى التواصل مع مرشد زراعي";
//     } catch (e) {
//       print("Error in analyzePlantSymptoms: $e");
//       return "عذرًا، حدث خطأ في تحليل الأعراض النباتية";
//     }
//   }
//
//   /// خاص بالزراعة - الحصول على نصائح موسمية
//   Future<String> getSeasonalAdvice(String cropType, String region) async {
//     if (!_isInitialized || _dialogflow == null) {
//       await initialize();
//     }
//
//     try {
//       final response = await _dialogflow!.detectIntent(
//         "نصائح زراعية لمحصول $cropType في منطقة $region",
//         context: "seasonal-advice-context",
//       );
//
//       return response.queryResult.fulfillmentText ??
//           "لا توجد نصائح متاحة حالياً لهذا المحصول في منطقتك";
//     } catch (e) {
//       print("Error in getSeasonalAdvice: $e");
//       return "عذرًا، حدث خطأ في جلب النصائح الزراعية";
//     }
//   }
// }