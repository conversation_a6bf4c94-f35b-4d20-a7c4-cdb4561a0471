// import 'package:flutter/foundation.dart'; // For kIsWeb
// import 'package:image_picker/image_picker.dart';
// import 'dart:io'; // For File
// import 'package:tflite_flutter/tflite_flutter.dart' as tfl;
// import 'package:flutter/services.dart' show rootBundle;
//
// /// خدمة لتحليل صور النباتات باستخدام نموذج TensorFlow Lite.
// /// تتطلب نموذج TFLite مدرب مسبقًا لتشخيص أمراض النباتات.
// class ImageAnalysisService {
//   Interpreter? _interpreter;
//   List<String>? _labels;
//   bool _isInitialized = false;
//   static const String _modelPath = 'assets/plant_disease_model.tflite'; // مسار النموذج
//   static const String _labelsPath = 'assets/plant_disease_labels.txt'; // مسار ملف التسميات
//
//   /// تهيئة الخدمة عن طريق تحميل نموذج TFLite والتسميات.
//   /// يجب استدعاؤها مرة واحدة قبل استخدام الخدمة.
//   Future<void> initialize() async {
//     if (_isInitialized) return;
//     // تجنب التحميل على الويب إذا كانت الحزمة لا تدعمها بشكل كامل
//     if (kIsWeb) {
//        print("تحليل الصور غير مدعوم حاليًا على الويب في هذا المثال.");
//        return;
//     }
//     try {
//       // تحميل النموذج
//       _interpreter = await tfl.Interpreter.fromAsset(_modelPath);
//       // تحميل التسميات
//       await _loadLabels();
//       _isInitialized = true;
//       print("خدمة تحليل الصور (TFLite) تم تهيئتها بنجاح.");
//     } catch (e) {
//       print("فشل تهيئة خدمة تحليل الصور (TFLite): $e");
//       print("تأكد من وجود ملفي النموذج '$_modelPath' والتسميات '$_labelsPath' في مجلد assets وإضافتهما إلى pubspec.yaml.");
//     }
//   }
//
//   /// تحميل التسميات من ملف النص.
//   Future<void> _loadLabels() async {
//     final labelsData = await rootBundle.loadString(_labelsPath);
//     _labels = labelsData.split('\n').map((label) => label.trim()).where((label) => label.isNotEmpty).toList();
//   }
//
//   /// تحليل صورة وتشخيص المرض المحتمل.
//   ///
//   /// [imageFile] ملف الصورة المراد تحليلها.
//   /// ترجع نص التشخيص أو رسالة خطأ.
//   Future<String> analyzeImage(File imageFile) async {
//     if (!_isInitialized || _interpreter == null || _labels == null) {
//       await initialize(); // محاولة التهيئة إذا لم تكن قد تمت
//       if (!_isInitialized || _interpreter == null || _labels == null) {
//         return "خطأ: خدمة تحليل الصور غير مهيأة.";
//       }
//     }
//
//     try {
//       // --- خطوات معالجة الصورة وتحليلها باستخدام TFLite ---
//       // 1. قراءة الصورة وتحويلها إلى تنسيق مناسب للنموذج (مثل TensorImage)
//       //    قد تحتاج إلى تغيير حجم الصورة، تطبيع القيم، إلخ.
//       //    مثال مبسط جداً (يحتاج لتفاصيل حسب نموذجك):
//       //    var image = img.decodeImage(imageFile.readAsBytesSync())!;
//       //    var resizedImage = img.copyResize(image, width: 224, height: 224);
//       //    var imageBytes = resizedImage.getBytes();
//       //    var inputBuffer = Float32List(1 * 224 * 224 * 3);
//       //    // ... تعبئة inputBuffer بقيم البكسلات المطبعة ...
//
//       // 2. تجه يز مخرجات النموذج
//       //    var outputShape = _interpreter!.getOutputTensor(0).shape;
//       //    var outputType = _interpreter!.getOutputTensor(0).type;
//       //    var outputBuffer = TensorBuffer.createFixedSize(outputShape, outputType);
//       var output = List.filled(_labels!.length, 0.0).reshape([1, _labels!.length]); // مثال لشكل المخرجات
//
//       // 3. تشغيل النموذج
//       //    _interpreter!.run(inputBuffer.buffer, output.buffer);
//       //    محاكاة نتيجة مؤقتة
//       await Future.delayed(const Duration(seconds: 1)); // محاكاة وقت التحليل
//       print("محاكاة تحليل الصورة...");
//       // الحصول على النتيجة الأعلى احتمالاً (مثال)
//       // final results = output.buffer.asFloat32List();
//       // int maxIndex = results.indexWhere((element) => element == results.reduce(max));
//       // double confidence = results[maxIndex];
//       // String diagnosis = _labels![maxIndex];
//
//       // نتيجة محاكاة
//       String diagnosis = _labels!.isNotEmpty ? _labels![0] : "مرض غير معروف";
//       double confidence = 0.75;
//
//       // 4. إرجاع التشخيص
//       if (confidence > 0.5) { // مثال: عتبة الثقة
//         return "تم تحليل الصورة. التشخيص المحتمل: $diagnosis (بثقة ${(confidence * 100).toStringAsFixed(1)}%). يرجى استشارة خبير للتأكيد.";
//       } else {
//         return "لم أتمكن من تحديد المرض بثقة كافية من الصورة. هل يمكنك التقاط صورة أوضح؟";
//       }
//
//     } catch (e) {
//       print("خطأ أثناء تحليل الصورة: $e");
//       return "عذرًا، حدث خطأ أثناء تحليل الصورة.";
//     }
//   }
//
//   /// تحرير موارد النموذج عند عدم الحاجة إليها.
//   void dispose() {
//     _interpreter?.close();
//     _isInitialized = false;
//   }
// }
//
// // --- ملاحظات هامة ---
// // 1. النموذج والتسميات: تحتاج إلى توفير ملف نموذج TFLite مدرب (`.tflite`) وملف تسميات (`.txt`) يتوافق معه.
// // 2. معالجة الصورة: خطوة معالجة الصورة قبل إدخالها للنموذج (تغيير الحجم، التطبيع) تعتمد كليًا على متطلبات النموذج الذي تستخدمه.
// // 3. التبعيات: تأكد من إضافة `tflite_flutter` و `image` (إذا استخدمتها للمعالجة) إلى `pubspec.yaml`.
// // 4. الأصول (Assets): تأكد من إضافة ملفات النموذج والتسميات إلى مجلد `assets` وتعريفها في `pubspec.yaml`.
// // 5. بدائل: يمكن استخدام واجهات برمجة تطبيقات سحابية (Cloud APIs) لتحليل الصور إذا كان النموذج كبيرًا جدًا أو يتطلب موارد حسابية عالية.
//

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tflite_flutter/tflite_flutter.dart' as tfl;
import 'dart:io';
import 'package:flutter/services.dart';

class ImageAnalysisService {
  late tfl.Interpreter _interpreter;
  List<String> _labels = [];
  bool _isInitialized = false;
  final _modelPath = 'assets/plant_disease_model.tflite';
  final _labelsPath = 'assets/plant_labels.txt';

  Future<void> initialize() async {
    if (_isInitialized) return;
    if (kIsWeb) {
      debugPrint("⚠️ Image analysis not supported on web");
      return;
    }

    try {
      // 1. تحميل النموذج
      _interpreter = await tfl.Interpreter.fromAsset(_modelPath);

      // 2. تحميل التسميات
      await _loadLabels();

      _isInitialized = true;
      debugPrint("✅ Plant analysis model loaded successfully");
    } catch (e) {
      debugPrint("❌ Failed to load plant model: $e");
      throw Exception("فشل تحميل نموذج تحليل النباتات");
    }
  }

  Future<void> _loadLabels() async {
    try {
      final labelData = await rootBundle.loadString(_labelsPath);
      _labels = labelData.split('\n').map((label) => label.trim()).toList();
    } catch (e) {
      debugPrint("Error loading labels: $e");
      _labels = ["غير معروف", "صحي", "مريض"];
    }
  }

  Future<String> analyzePlantImage(File imageFile) async {
    if (!_isInitialized) await initialize();

    try {
      // 1. معالجة الصورة
      final imageBytes = await imageFile.readAsBytes();
      final imageTensor = _preprocessImage(imageBytes);

      // 2. تشغيل النموذج
      final output = List.filled(_labels.length, 0.0).reshape([1, _labels.length]);
      _interpreter.run(imageTensor, output);

      // 3. تفسير النتائج
      final results = output[0];
      final maxIndex = results.indexOf(results.reduce((a, b) => a > b ? a : b));
      final confidence = results[maxIndex];
      final diagnosis = _labels[maxIndex];

      return _formatDiagnosis(diagnosis, confidence);
    } catch (e) {
      debugPrint("Error analyzing image: $e");
      return "تعذر تحليل صورة النبات. يرجى المحاولة مرة أخرى";
    }
  }

  List<List<List<List<String>>>> _preprocessImage(List<int> imageBytes) {
    // TODO: تطبيق معالجة الصورة حسب متطلبات النموذج
    // هذه دمية - استبدلها بمعالجة حقيقية
    return List.filled(224, List.filled(224, List.filled(3,null!)));
  }

  String _formatDiagnosis(String diagnosis, double confidence) {
    final confidencePercent = (confidence * 100).toStringAsFixed(1);
    final arabicDiagnosis = _translateToArabic(diagnosis);

    return """
التشخيص: $arabicDiagnosis
الدقة: $confidencePercent%
${_getTreatmentAdvice(diagnosis)}
""";
  }

  String _translateToArabic(String diagnosis) {
    // ترجمة بسيطة - يمكن استبدالها بقاعدة بيانات
    const translations = {
      'healthy': 'نبات صحي',
      'disease': 'مرض نباتي',
      'pest': 'آفات زراعية',
      'nutrient_deficiency': 'نقص تغذية'
    };
    return translations[diagnosis.toLowerCase()] ?? diagnosis;
  }

  String _getTreatmentAdvice(String diagnosis) {
    // نصائح علاجية حسب التشخيص
    switch (diagnosis.toLowerCase()) {
      case 'healthy':
        return "نصائح: النبات في حالة جيدة، استمر في العناية به";
      case 'disease':
        return "نصائح: عزل النبات المصاب، استخدام مبيد فطري مناسب";
      case 'pest':
        return "نصائح: رش بالمبيد الحشري العضوي كل 3 أيام";
      case 'nutrient_deficiency':
        return "نصائح: إضافة سماد متوازن NPK حسب احتياج النبات";
      default:
        return "نصائح: استشر مرشد زراعي للحصول على علاج دقيق";
    }
  }

  void dispose() {
    _interpreter.close();
    _isInitialized = false;
  }
}