import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";

import "../../bloc/advisor_cubit.dart";
import "../../bloc/advisor_state.dart";
import "../../core/constants/app_strings.dart";
import "../advisor_dashboard/components/appointment_card.dart";

/// شاشة عرض قائمة المواعيد.
class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  String _selectedFilter = "الكل"; // فلتر افتراضي

  @override
  void initState() {
    super.initState();
    // طلب جلب المواعيد عند تحميل الشاشة
    // *** سيتم إصلاح الدالة المفقودة في الخطوة التالية ***
    // context.read<AdvisorCubit>().fetchAppointments();
  }

  // دالة لتطبيق الفلتر (مثال)
  void _applyFilter(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    // يمكنك هنا استدعاء دالة في الـ Cubit لجلب البيانات المفلترة
    // context.read<AdvisorCubit>().fetchAppointments(filter: filter);
    print("تم تطبيق فلتر: $filter");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // *** سيتم إصلاح المتغير المفقود في الخطوة التالية ***
        title: const Text(AppStrings.appointmentsTitle), // عنوان الشاشة
        actions: [
          // زر الفلترة
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            tooltip: "فلترة المواعيد",
            onSelected: _applyFilter,
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(value: "الكل", child: Text("الكل")),
              const PopupMenuItem<String>(value: "اليوم", child: Text("اليوم")),
              const PopupMenuItem<String>(value: "الأسبوع", child: Text("الأسبوع")),
              const PopupMenuItem<String>(value: "الشهر", child: Text("الشهر")),
            ],
          ),
        ],
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          if (state is AdvisorLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is AdvisorLoaded) {
            // *** سيتم إصلاح الخاصية المفقودة في الخطوة التالية ***
            final appointments = state.appointments;
            // يمكنك فلترة المواعيد هنا بناءً على _selectedFilter إذا لم يتم الفلترة في الـ Cubit
            final filteredAppointments = appointments;

            if (filteredAppointments.isEmpty) {
              return const Center(child: Text("لا توجد مواعيد حاليًا."));
            }
            return ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: filteredAppointments.length,
              itemBuilder: (context, index) {
                final appointment = filteredAppointments[index];
                return AppointmentCard(appointment: appointment);
              },
            );
          } else if (state is AdvisorError) {
            return Center(child: Text("خطأ: ${state.message}"));
          } else {
            // الحالة الأولية أو غير معروفة
            return const Center(child: Text("جارٍ تحميل المواعيد..."));
          }
        },
      ),
    );
  }
}


