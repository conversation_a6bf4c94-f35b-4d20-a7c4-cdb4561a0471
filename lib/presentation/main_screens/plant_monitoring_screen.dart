import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";

import "../../bloc/advisor_cubit.dart";
import "../../bloc/advisor_state.dart";
import "../../core/constants/app_strings.dart";
import "../advisor_dashboard/components/appointment_card.dart";
import "../advisor_dashboard/components/plant_card.dart";

/// شاشة عرض قائمة مراقبة النباتات.
class PlantMonitoringScreen extends StatefulWidget {
  const PlantMonitoringScreen({super.key});

  @override
  State<PlantMonitoringScreen> createState() => _PlantMonitoringScreenState();
}

class _PlantMonitoringScreenState extends State<PlantMonitoringScreen> {
  @override
  void initState() {
    super.initState();
    // طلب جلب بيانات مراقبة النباتات عند تحميل الشاشة
    // *** سيتم إصلاح الدالة المفقودة في الخطوة التالية ***
    // context.read<AdvisorCubit>().fetchPlantMonitorings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // *** سيتم إصلاح المتغير المفقود في الخطوة التالية ***
        title: const Text(AppStrings.plantMonitoringTitle), // عنوان الشاشة
        // يمكنك إضافة أزرار فلترة أو إضافة هنا إذا أردت
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          if (state is AdvisorLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is AdvisorLoaded) {
            // *** سيتم إصلاح الخاصية المفقودة في الخطوة التالية ***
            final plantMonitorings = state.plantMonitorings;
            if (plantMonitorings.isEmpty) {
              return const Center(child: Text("لا توجد نباتات قيد المراقبة حاليًا."));
            }
            // استخدام GridView لعرض البطاقات بشكل شبكي
            return GridView.builder(
              padding: const EdgeInsets.all(12.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, // عدد الأعمدة
                crossAxisSpacing: 12.0, // المسافة الأفقية بين البطاقات
                mainAxisSpacing: 12.0, // المسافة الرأسية بين البطاقات
                childAspectRatio: 0.8, // نسبة العرض إلى الارتفاع للبطاقة
              ),
              itemCount: plantMonitorings.length,
              itemBuilder: (context, index) {
                final plantMonitoring = plantMonitorings[index];
                // *** سيتم إصلاح استدعاء الويدجت في الخطوة التالية ***
                return PlantCard(plantData: plantMonitoring);
              },
            );
          } else if (state is AdvisorError) {
            return Center(child: Text("خطأ: ${state.message}"));
          } else {
            // الحالة الأولية أو غير معروفة
            return const Center(child: Text("جارٍ تحميل بيانات المراقبة..."));
          }
        },
      ),
    );
  }
}


