import "package:flutter/material.dart";

import "../../core/constants/app_strings.dart";

/// شاشة عرض التقارير.
class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // *** سيتم إصلاح المتغير المفقود في الخطوة التالية ***
        title: const Text(AppStrings.reportsTitle), // عنوان الشاشة
      ),
      body: const Center(
        child: Text(
          "شاشة التقارير (قيد التطوير)",
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      ),
      // يمكنك إضافة زر عائم هنا لتصدير التقارير مثلاً
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     // منطق تصدير التقرير
      //   },
      //   tooltip: 'تصدير تقرير',
      //   child: const Icon(Icons.download),
      // ),
    );
  }
}


