import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";
import "../../bloc/advisor_cubit.dart";
import "../../bloc/advisor_state.dart";
import "../../core/constants/app_strings.dart";
import "../advisor_dashboard/components/consultation_card.dart";

/// شاشة عرض قائمة الاستشارات.
class ConsultationsScreen extends StatefulWidget {
  const ConsultationsScreen({super.key});

  @override
  State<ConsultationsScreen> createState() => _ConsultationsScreenState();
}

class _ConsultationsScreenState extends State<ConsultationsScreen> {
  @override
  void initState() {
    super.initState();
    // طلب جلب الاستشارات عند تحميل الشاشة
    // *** سيتم إصلاح الدالة المفقودة في الخطوة التالية ***
    // context.read<AdvisorCubit>().fetchConsultations();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // *** سيتم إصلاح المتغير المفقود في الخطوة التالية ***
        title: const Text(AppStrings.consultationsTitle), // عنوان الشاشة
        // يمكنك إضافة أزرار فلترة هنا إذا أردت
      ),
      body: BlocBuilder<AdvisorCubit, AdvisorState>(
        builder: (context, state) {
          if (state is AdvisorLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is AdvisorLoaded) {
            // *** سيتم إصلاح الخاصية المفقودة في الخطوة التالية ***
            final consultations = state.consultations;
            if (consultations.isEmpty) {
              return const Center(child: Text("لا توجد استشارات حاليًا."));
            }
            return ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: consultations.length,
              itemBuilder: (context, index) {
                final consultation = consultations[index];
                return ConsultationCard(consultation: consultation);
              },
            );
          } else if (state is AdvisorError) {
            return Center(child: Text("خطأ: ${state.message}"));
          } else {
            // الحالة الأولية أو غير معروفة
            return const Center(child: Text("جارٍ تحميل الاستشارات..."));
          }
        },
      ),
    );
  }
}

