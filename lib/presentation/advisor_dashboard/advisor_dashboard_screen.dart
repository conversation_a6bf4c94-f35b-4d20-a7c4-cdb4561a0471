import 'package:agriculture/presentation/main_screens/reports_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/bloc/advisor_cubit.dart'; // Cubit الرئيسي للتطبيق
import 'package:agriculture/presentation/advisor_dashboard/tabs/consultations_tab.dart';
import 'package:agriculture/presentation/advisor_dashboard/tabs/appointments_tab.dart';
import 'package:agriculture/presentation/advisor_dashboard/tabs/plant_monitoring_tab.dart';
import 'package:agriculture/presentation/advisor_dashboard/tabs/reports_tab.dart';
import 'package:agriculture/core/widgets/stat_card.dart';
import 'package:agriculture/presentation/smart_assistant/smart_assistant_screen.dart'; // شاشة المرشد الذكي
import 'package:agriculture/presentation/smart_assistant/cubit/smart_assistant_cubit.dart';
import '../../bloc/advisor_state.dart';
import '../../core/constants/app_strings.dart';
//عدلت زيل تعليق
/// الشاشة الرئيسية للوحة تحكم المرشد الزراعي.
class AdvisorDashboardScreen extends StatefulWidget {
  const AdvisorDashboardScreen({super.key});

  @override
  State<AdvisorDashboardScreen> createState() => _AdvisorDashboardScreenState();
}

class _AdvisorDashboardScreenState extends State<AdvisorDashboardScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // 4 تبويبات
    // يمكنك استدعاء جلب البيانات الأولية هنا إذا لزم الأمر
    // context.read<AdvisorCubit>().fetchInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.dashboardTitle),
        actions: [
          // زر الإشعارات (مثال)
          IconButton(
            icon: const Icon(Icons.notifications_none),
            tooltip: 'الإشعارات',
            onPressed: () {
              // TODO: عرض شاشة الإشعارات
            },
          ),
          // زر المرشد الذكي
          IconButton(
            icon: const Icon(Icons.smart_toy_outlined), // أيقونة الروبوت
            tooltip: 'المرشد الذكي',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider(
                    // توفير Cubit المرشد الذكي عند الانتقال للشاشة
                    // تأكد من أن الخدمات (Dialogflow, ImageAnalysis) مهيأة
                    create: (context) => SmartAssistantCubit(),
                    child: const SmartAssistantScreen(),
                  ),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true, // للسماح بالتمرير إذا كانت التبويبات كثيرة
          tabs: const [
            Tab(icon: Icon(Icons.chat_bubble_outline), text: AppStrings.consultationsTab),
            Tab(icon: Icon(Icons.calendar_today_outlined), text: AppStrings.appointmentsTab),
            Tab(icon: Icon(Icons.eco_outlined), text: AppStrings.plantMonitoringTab),
            Tab(icon: Icon(Icons.bar_chart_outlined), text: AppStrings.reportsTab),
          ],
        ),
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // قسم معلومات المرشد (مثال)
                    const Row(
                      children: [
                        CircleAvatar(child: Icon(Icons.person_outline)),
                        SizedBox(width: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('د. أحمد محمد', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('🟢 متاح الآن', style: TextStyle(color: Colors.green)),
                            Text('⭐ تقييم: 4.8/5', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // قسم الإحصائيات السريعة
                    const Text(AppStrings.quickStatsTitle, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 10),
                    // استخدام BlocBuilder لجلب الإحصائيات من Cubit الرئيسي
                    BlocBuilder<AdvisorCubit, AdvisorState>(
                      builder: (context, state) {
                        // قيم افتراضية أو من الحالة
                        int newConsultations = 0;
                        int pendingConsultations = 0;
                        int urgentConsultations = 0;
                        int completedConsultations = 0;
                        if (state is AdvisorLoaded) {
                          // افتراض أن الحالة تحتوي على هذه الإحصائيات
                          // newConsultations = state.stats.newConsultations;
                          // ... وهكذا
                        }
                        // يمكنك عرض مؤشر تحميل هنا إذا كانت الحالة AdvisorLoading
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            StatCard(label: AppStrings.statNew, count: newConsultations.toString(), color: Colors.blue),
                            StatCard(label: AppStrings.statPending, count: pendingConsultations.toString(), color: Colors.orange),
                            StatCard(label: AppStrings.statUrgent, count: urgentConsultations.toString(), color: Colors.red),
                            StatCard(label: AppStrings.statCompleted, count: completedConsultations.toString(), color: Colors.green),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
             ConsultationsTab(),
             AppointmentsTab(),
             PlantMonitoringTab(),
              ReportsScreen(),
          ],
        ),
      ),
      // يمكنك إضافة زر عائم هنا إذا أردت (مثل إضافة استشارة جديدة)
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        child: const Icon(Icons.add),
      ),
    );
  }
}


// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:agriculture/core/constants/app_strings.dart';
// import 'package:agriculture/core/widgets/stat_card.dart';
// import 'package:agriculture/presentation/smart_assistant/smart_assistant_screen.dart';
// import 'package:agriculture/presentation/smart_assistant/cubit/smart_assistant_cubit.dart';
// import 'package:agriculture/bloc/advisor_cubit.dart';
// import 'package:agriculture/bloc/advisor_state.dart';
// import '../smart_assistant/widgets/smart.dart';
// import 'tabs/consultations_tab.dart';
// import 'tabs/appointments_tab.dart';
// import 'tabs/plant_monitoring_tab.dart';
// import 'tabs/reports_tab.dart';
//
// class AdvisorDashboardScreen extends StatefulWidget {
//   const AdvisorDashboardScreen({super.key});
//
//   @override
//   State<AdvisorDashboardScreen> createState() => _AdvisorDashboardScreenState();
// }
//
// class _AdvisorDashboardScreenState extends State<AdvisorDashboardScreen>
//     with SingleTickerProviderStateMixin {
//   late TabController _tabController;
//
//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 4, vsync: this);
//     _loadInitialData();
//   }
//
//   void _loadInitialData() {
//     context.read<AdvisorCubit>().fetchInitialData();
//   }
//
//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(AppStrings.dashboardTitle),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.notifications_none),
//             onPressed: _showNotifications,
//           ),
//           IconButton(
//             icon: const Icon(Icons.smart_toy_outlined),
//             onPressed: _openSmartAssistant,
//           ),
//         ],
//         bottom: TabBar(
//           controller: _tabController,
//           isScrollable: true,
//           tabs: const [
//             Tab(icon: Icon(Icons.chat_bubble_outline), text: AppStrings.consultationsTab),
//             Tab(icon: Icon(Icons.calendar_today_outlined), text: AppStrings.appointmentsTab),
//             Tab(icon: Icon(Icons.eco_outlined), text: AppStrings.plantMonitoringTab),
//             Tab(icon: Icon(Icons.bar_chart_outlined), text: AppStrings.reportsTab),
//           ],
//         ),
//       ),
//       body: NestedScrollView(
//         headerSliverBuilder: (context, innerBoxIsScrolled) {
//           return [_buildHeaderSliver()];
//         },
//         body: TabBarView(
//           controller: _tabController,
//           children: const [
//             ConsultationsTab(),
//             AppointmentsTab(),
//             PlantMonitoringTab(),
//             ReportsTab(),
//           ],
//         ),
//       ),
//     );
//   }
//
//   SliverToBoxAdapter _buildHeaderSliver() {
//     return SliverToBoxAdapter(
//       child: BlocBuilder<AdvisorCubit, AdvisorState>(
//         builder: (context, state) {
//           return Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildAdvisorInfo(),
//                 const SizedBox(height: 20),
//                 _buildQuickStats(state),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
//
//   Widget _buildAdvisorInfo() {
//     return const Row(
//       children: [
//         CircleAvatar(child: Icon(Icons.person_outline)),
//         SizedBox(width: 10),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text('د. أحمد محمد', style: TextStyle(fontWeight: FontWeight.bold)),
//             Text('🟢 متاح الآن', style: TextStyle(color: Colors.green)),
//             Text('⭐ تقييم: 4.8/5', style: TextStyle(color: Colors.grey)),
//           ],
//         ),
//       ],
//     );
//   }
//
//   Widget _buildQuickStats(AdvisorState state) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Text(AppStrings.quickStatsTitle,
//             style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
//         const SizedBox(height: 10),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             StatCard(label: AppStrings.statNew, count: '12', color: Colors.blue),
//             StatCard(label: AppStrings.statPending, count: '5', color: Colors.orange),
//             StatCard(label: AppStrings.statUrgent, count: '3', color: Colors.red),
//             StatCard(label: AppStrings.statCompleted, count: '24', color: Colors.green),
//           ],
//         ),
//       ],
//     );
//   }
//
//   void _showNotifications() {
//     // TODO: Implement notifications screen
//   }
//
//   void _openSmartAssistant() {
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (_) => BlocProvider(
//           create: (context) => SmartAssistantCubit(),
//           child: const SmartAssistantScreen(),
//         ),
//       ),
//     );
//   }
// }
