import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Ensure intl is added to pubspec.yaml
import 'package:agriculture/data/models/appointment.dart';
import 'package:agriculture/core/constants/colors.dart'; // Using placeholder colors

class AppointmentCard extends StatelessWidget {
  final AppointmentModel appointment;

  const AppointmentCard({super.key, required this.appointment});

  // Helper to get icon based on appointment type
  IconData _getAppointmentTypeIcon(String type) {
    switch (type) {
      case 'video_call':
        return Icons.videocam;
      case 'field_visit':
        return Icons.location_pin; // Or a tractor icon?
      case 'online_meeting':
        return Icons.laptop_mac;
      default:
        return Icons.event;
    }
  }

  // Helper to get type text
  String _getAppointmentTypeText(String type) {
    switch (type) {
      case 'video_call':
        return 'اتصال فيديو';
      case 'field_visit':
        return 'زيارة ميدانية';
      case 'online_meeting':
        return 'اجتماع أونلاين';
      default:
        return 'موعد';
    }
  }

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat('hh:mm a', 'ar'); // Arabic time format
    final formattedTime = timeFormat.format(appointment.dateTime);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time and Topic
            Text(
              '$formattedTime - ${appointment.topic}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            // User Name and Type
            Row(
              children: [
                const Icon(Icons.person_outline, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(appointment.userName),
                const Spacer(),
                Icon(_getAppointmentTypeIcon(appointment.type), size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(_getAppointmentTypeText(appointment.type)),
              ],
            ),
            const SizedBox(height: 8),
            // Location and Duration
            Row(
              children: [
                const Icon(Icons.location_on_outlined, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(appointment.location),
                const Spacer(),
                const Icon(Icons.timer_outlined, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text('${appointment.durationMinutes} دقيقة'),
              ],
            ),
            const SizedBox(height: 12),
            // Action Buttons (Example based on status)
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    // Buttons might change based on appointment.status
    List<Widget> buttons = [];

    if (appointment.status == 'scheduled') {
      buttons = [
        FilledButton(onPressed: _startAppointment, child: const Text('بدء')),
        OutlinedButton(onPressed: _rescheduleAppointment, child: const Text('تأجيل')),
        TextButton(onPressed: _cancelAppointment, child: const Text('إلغاء', style: TextStyle(color: Colors.red))),
      ];
    } else if (appointment.status == 'pending_confirmation') {
       buttons = [
        FilledButton(onPressed: _confirmAppointment, child: const Text('تأكيد')),
        TextButton(onPressed: _cancelAppointment, child: const Text('رفض', style: TextStyle(color: Colors.red))),
      ];
    } else {
      // Example for completed/cancelled
      buttons = [
        OutlinedButton(onPressed: _viewDetails, child: const Text('عرض التفاصيل')),
      ];
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: buttons,
    );
  }

  // Placeholder action methods
  void _startAppointment() {
    // Implement start logic (e.g., open video call link)
  }

  void _rescheduleAppointment() {
    // Implement reschedule logic (e.g., open calendar)
  }

  void _cancelAppointment() {
    // Implement cancellation logic
  }

   void _confirmAppointment() {
    // Implement confirmation logic
  }

  void _viewDetails() {
    // Implement view details logic
  }
}

