import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/colors.dart';
import 'package:agriculture/core/utils/date_utils.dart';
import 'package:agriculture/data/models/consultation.dart';

class ConsultationCard extends StatelessWidget {
  final ConsultationModel consultation;

  const ConsultationCard({super.key, required this.consultation});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 8),
            Text(
              consultation.problemDescription,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 12),
            _buildFooter(),
            const SizedBox(height: 12),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildStatusIndicator(),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            consultation.cropType,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),
        Text(
          formatTimeAgo(consultation.createdAt), // Uses date_utils
          style: const TextStyle(color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    Color color;
    String statusText;

    switch (consultation.status) {
      case 'urgent':
        color = AppColors.urgent;
        statusText = 'عاجل';
        break;
      case 'pending':
        color = AppColors.warning;
        statusText = 'جديد';
        break;
      case 'answered':
        color = AppColors.success;
        statusText = 'تم الرد';
        break;
      default:
        color = Colors.grey;
        statusText = consultation.status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            const Icon(Icons.person, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(consultation.userName),
          ],
        ),
        Row(
          children: [
            const Icon(Icons.location_on, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(consultation.location), // Use location from model
          ],
        ),
        Row(
          children: [
            const Icon(Icons.camera_alt, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text('${consultation.imagesCount} صور'), // Use imagesCount from model
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        OutlinedButton(
          onPressed: _viewDetails,
          child: const Text('عرض التفاصيل'),
        ),
        FilledButton(
          onPressed: _quickReply,
          style: FilledButton.styleFrom(
            backgroundColor: AppColors.primary,
          ),
          child: const Text('رد سريع'),
        ),
        OutlinedButton(
          onPressed: _transferToExpert,
          child: const Text('تحويل لخبير'),
        ),
      ],
    );
  }

  void _viewDetails() {
    // عرض تفاصيل الاستشارة
    // Implement navigation or dialog display here
  }

  void _quickReply() {
    // الرد السريع على الاستشارة
    // Implement quick reply logic here
  }

  void _transferToExpert() {
    // تحويل الاستشارة إلى خبير
    // Implement transfer logic here
  }
}

