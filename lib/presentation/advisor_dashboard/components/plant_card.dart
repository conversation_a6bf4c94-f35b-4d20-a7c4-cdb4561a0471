import "package:flutter/material.dart";
import "package:intl/intl.dart"; // لتنسيق التاريخ
import "package:agriculture/core/constants/app_colors.dart"; // <-- تم نقل الاستيراد للأعلى
// import "../../../core/constants/colors.dart"; // <-- تم التعليق/الحذف: استيراد مكرر أو غير صحيح
import "../../../core/widgets/status_chip.dart"; // <-- تم التعديل: مسار نسبي
import "../../../data/models/plant_monitoring.dart"; // <-- تم التعديل: مسار نسبي

/// بطاقة لعرض معلومات نبات واحد قيد المراقبة.
class PlantCard extends StatelessWidget {
  // *** تم التعديل: قبول PlantMonitoringModel ***
  final PlantMonitoringModel plantData;

  const PlantCard({
    super.key,
    required this.plantData,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد لون الحالة بناءً على حالة النبات
    Color statusColor;
    switch (plantData.status.toLowerCase()) {
      case "صحي":
        statusColor = AppColors.statusHealthy;
        break;
      case "تحت الملاحظة":
        statusColor = AppColors.statusWarning;
        break;
      case "حرج":
        statusColor = AppColors.statusCritical;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: InkWell(
        onTap: () {
          // TODO: الانتقال إلى شاشة تفاصيل النبات
          print("تم النقر على نبات: ${plantData.plantName}");
        },
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة النبات (مثال - تحتاج لمصدر صورة حقيقي)
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.0),
                    // يمكنك إضافة صورة هنا
                    // image: DecorationImage(
                    //   image: NetworkImage(plantData.imageUrl ?? 
                    //     "https://via.placeholder.com/150/2e7d32/ffffff?text=Plant"), // صورة افتراضية
                    //   fit: BoxFit.cover,
                    // ),
                  ),
                  alignment: Alignment.center,
                  child: plantData.imageUrl == null
                      ? Icon(Icons.local_florist_outlined, size: 40, color: Colors.grey[400])
                      : Image.network(plantData.imageUrl!, fit: BoxFit.cover, // TODO: Handle image loading errors
                         errorBuilder: (context, error, stackTrace) => Icon(Icons.broken_image, size: 40, color: Colors.grey[400]),
                        ), // عرض الصورة إذا كانت متوفرة
                ),
              ),
              const SizedBox(height: 10.0),
              // اسم النبات
              Text(
                plantData.plantName,
                style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4.0),
              // نوع المحصول
              Text(
                plantData.cropType,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 6.0),
              // حالة النبات
              StatusChip(statusText: plantData.status, backgroundColor: statusColor),
              const SizedBox(height: 4.0),
              // تاريخ آخر تحديث
              Text(
                "آخر تحديث: ${DateFormat("yyyy/MM/dd").format(plantData.lastUpdate)}",
                style: TextStyle(fontSize: 10, color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
