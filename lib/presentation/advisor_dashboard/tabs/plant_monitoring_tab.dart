// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:agriculture/bloc/advisor_cubit.dart';
// import 'package:agriculture/bloc/advisor_state.dart';
// import 'package:agriculture/data/models/plant_monitoring.dart';
// import 'package:agriculture/presentation/advisor_dashboard/components/plant_card.dart';
//
// /// تبويب عرض النباتات المراقبة للمرشد.
// /// يستخدم AdvisorCubit لجلب وعرض قائمة النباتات.
// class PlantMonitoringTab extends StatefulWidget {
//   const PlantMonitoringTab({super.key});
//
//   @override
//   State<PlantMonitoringTab> createState() => _PlantMonitoringTabState();
// }
//
// class _PlantMonitoringTabState extends State<PlantMonitoringTab> {
//   // حالة محلية للبحث والتصفية
//   String _searchQuery = '';
//   String _filterStatus = 'الكل'; // فلتر الحالة الافتراضي
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         _buildSearchBarAndFilter(),
//         Expanded(
//           // استخدام BlocBuilder للاستماع إلى حالات AdvisorCubit
//           child: BlocBuilder<AdvisorCubit, AdvisorState>(
//             builder: (context, state) {
//               // عرض مؤشر التحميل
//               if (state is AdvisorLoading || state is AdvisorInitial) {
//                 return const Center(child: CircularProgressIndicator());
//               }
//               // عرض رسالة خطأ
//               if (state is AdvisorError) {
//                 return Center(
//                   child: Padding(
//                     padding: const EdgeInsets.all(16.0),
//                     child: Text(
//                       'خطأ: ${state.message}\nيرجى التأكد من تهيئة Firebase والمحاولة مرة أخرى.',
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                 );
//               }
//               // عرض البيانات بعد تحميلها
//               if (state is AdvisorLoaded) {
//                 // تطبيق الفلاتر المحلية على البيانات المحملة
//                 final filteredPlants = _filterPlants(state.monitoredPlants);
//
//                 // عرض رسالة إذا لم تكن هناك نباتات مطابقة
//                 if (filteredPlants.isEmpty) {
//                   return const Center(child: Text('لا توجد نباتات للمراقبة تطابق البحث أو الفلتر الحالي.'));
//                 }
//
//                 // عرض قائمة النباتات
//                 return ListView.builder(
//                   itemCount: filteredPlants.length,
//                   itemBuilder: (context, index) {
//                     return PlantCard(plantData: filteredPlants[index]);
//                   },
//                 );
//               }
//               // حالة غير متوقعة
//               return const Center(child: Text('حالة غير معروفة.'));
//             },
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// بناء شريط البحث وفلتر الحالة.
//   Widget _buildSearchBarAndFilter() {
//     return Padding(
//       padding: const EdgeInsets.all(12.0),
//       child: Row(
//         children: [
//           // حقل البحث
//           Expanded(
//             child: TextField(
//               onChanged: (value) {
//                 setState(() {
//                   _searchQuery = value;
//                 });
//               },
//               decoration: InputDecoration(
//                 hintText: 'ابحث في النباتات المراقبة...', // نص مساعد
//                 prefixIcon: const Icon(Icons.search),
//                 border: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(20),
//                   borderSide: BorderSide.none,
//                 ),
//                 filled: true,
//                 fillColor: Colors.grey[100],
//               ),
//             ),
//           ),
//           const SizedBox(width: 8),
//           // زر الفلتر (قائمة منسدلة)
//           _buildFilterDropdown(),
//         ],
//       ),
//     );
//   }
//
//   /// بناء القائمة المنسدلة لفلتر الحالة.
//   Widget _buildFilterDropdown() {
//     // قائمة الحالات الممكنة للفلترة (مطابقة للوصف المرئي والنموذج)
//     final List<String> statuses = ['الكل', 'حالة حرجة', 'حالة متوسطة', 'صحة جيدة', 'تحتاج متابعة'];
//
//     return DropdownButton<String>(
//       value: _filterStatus,
//       icon: const Icon(Icons.filter_list),
//       underline: Container(), // إزالة الخط السفلي
//       onChanged: (String? newValue) {
//         if (newValue != null) {
//           setState(() {
//             _filterStatus = newValue;
//           });
//         }
//       },
//       items: statuses.map<DropdownMenuItem<String>>((String value) {
//         return DropdownMenuItem<String>(
//           value: value,
//           child: Text(value),
//         );
//       }).toList(),
//     );
//   }
//
//   /// تطبيق البحث والفلترة على قائمة النباتات المراقبة.
//   List<PlantMonitoringModel> _filterPlants(List<PlantMonitoringModel> plants) {
//     List<PlantMonitoringModel> filteredList = plants;
//
//     // تطبيق فلتر الحالة
//     if (_filterStatus != 'الكل') {
//       // نحتاج لمطابقة النص المعروض مع القيم الفعلية في النموذج إذا كانت مختلفة
//       // هنا نفترض تطابقها أو يمكن استخدام switch case للمطابقة
//       filteredList = filteredList.where((p) => _getStatusText(p.status) == _filterStatus).toList();
//     }
//
//     // تطبيق البحث النصي
//     if (_searchQuery.isNotEmpty) {
//       final query = _searchQuery.toLowerCase();
//       filteredList = filteredList.where((p) {
//         return p.userName.toLowerCase().contains(query) ||
//                p.cropType.toLowerCase().contains(query) ||
//                p.location.toLowerCase().contains(query) ||
//                p.lastSymptoms.toLowerCase().contains(query);
//       }).toList();
//     }
//
//     return filteredList;
//   }
//
//   // دالة مساعدة لتحويل قيمة الحالة من النموذج إلى النص المعروض في الفلتر
//   // (مكررة من PlantCard، يمكن وضعها في ملف utils مشترك)
//   String _getStatusText(String status) {
//     switch (status.toLowerCase()) {
//       case 'critical':
//         return 'حالة حرجة';
//       case 'warning':
//         return 'حالة متوسطة';
//       case 'healthy':
//         return 'صحة جيدة';
//       case 'needs_attention':
//         return 'تحتاج متابعة';
//       default:
//         return status;
//     }
//   }
// }
//
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/bloc/advisor_cubit.dart';
import 'package:agriculture/bloc/advisor_state.dart';
import 'package:agriculture/data/models/plant_monitoring.dart';
import 'package:agriculture/presentation/advisor_dashboard/components/plant_card.dart';
//عدلت زيل تعليق
class PlantMonitoringTab extends StatefulWidget {
  const PlantMonitoringTab({super.key});

  @override
  State<PlantMonitoringTab> createState() => _PlantMonitoringTabState();
}

class _PlantMonitoringTabState extends State<PlantMonitoringTab> {
  String _searchQuery = '';
  String _filterStatus = 'الكل';

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBarAndFilter(),
        Expanded(
          child: BlocBuilder<AdvisorCubit, AdvisorState>(
            builder: (context, state) {
              if (state is AdvisorLoading || state is AdvisorInitial) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is AdvisorError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'خطأ: ${state.message}',
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }

              if (state is AdvisorLoaded) {
                final plants = state.monitoredPlants ?? [];
                final filteredPlants = _filterPlants(plants);

                if (filteredPlants.isEmpty) {
                  return const Center(child: Text('لا توجد نباتات للمراقبة'));
                }

                return ListView.builder(
                  itemCount: filteredPlants.length,
                  itemBuilder: (context, index) {
                    return PlantCard(plantData: filteredPlants[index]);
                  },
                );
              }

              return const Center(child: Text('حالة غير معروفة'));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBarAndFilter() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'ابحث في النباتات...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          _buildFilterDropdown(),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown() {
    final statuses = ['الكل', 'حالة حرجة', 'حالة متوسطة', 'صحة جيدة', 'تحتاج متابعة'];

    return DropdownButton<String>(
      value: _filterStatus,
      onChanged: (value) => setState(() => _filterStatus = value ?? 'الكل'),
      items: statuses.map((value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
    );
  }

  List<PlantMonitoringModel> _filterPlants(List<PlantMonitoringModel> plants) {
    var filtered = plants.where((plant) {
      final matchesSearch = _searchQuery.isEmpty ||
          plant.cropType.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          plant.location.toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesFilter = _filterStatus == 'الكل' ||
          _getStatusText(plant.status) == _filterStatus;

      return matchesSearch && matchesFilter;
    }).toList();

    return filtered;
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'critical': return 'حالة حرجة';
      case 'warning': return 'حالة متوسطة';
      case 'healthy': return 'صحة جيدة';
      case 'needs_attention': return 'تحتاج متابعة';
      default: return status;
    }
  }
}