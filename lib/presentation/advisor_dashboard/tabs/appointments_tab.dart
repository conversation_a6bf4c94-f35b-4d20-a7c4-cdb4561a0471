import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/bloc/advisor_cubit.dart';
import 'package:agriculture/bloc/advisor_state.dart';
import 'package:agriculture/presentation/advisor_dashboard/components/appointment_card.dart';
import 'package:agriculture/data/models/appointment.dart'; // لاستخدام النموذج
import 'package:agriculture/core/utils/date_utils.dart'; // لاستخدام تنسيق الوقت

/// تبويب عرض المواعيد للمرشد.
/// يستخدم AdvisorCubit لجلب وعرض قائمة المواعيد.
class AppointmentsTab extends StatefulWidget {
  const AppointmentsTab({super.key});

  @override
  State<AppointmentsTab> createState() => _AppointmentsTabState();
}

class _AppointmentsTabState extends State<AppointmentsTab> {
  // حالة محلية للفلترة (اليوم، الأسبوع، الشهر)
  String _filterPeriod = 'اليوم'; // الفلتر الافتراضي

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilterChips(),
        Expanded(
          // استخدام BlocBuilder للاستماع إلى حالات AdvisorCubit
          child: BlocBuilder<AdvisorCubit, AdvisorState>(
            builder: (context, state) {
              // عرض مؤشر التحميل
              if (state is AdvisorLoading || state is AdvisorInitial) {
                return const Center(child: CircularProgressIndicator());
              }
              // عرض رسالة خطأ
              if (state is AdvisorError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'خطأ: ${state.message}\nيرجى التأكد من تهيئة Firebase والمحاولة مرة أخرى.',
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }
              // عرض البيانات بعد تحميلها
              if (state is AdvisorLoaded) {
                // تطبيق الفلتر المحلي على المواعيد
                final filteredAppointments = _filterAppointments(state.appointments);

                // عرض رسالة إذا لم تكن هناك مواعيد مطابقة
                if (filteredAppointments.isEmpty) {
                  return Center(child: Text('لا توجد مواعيد مجدولة لـ $_filterPeriod.'));
                }

                // عرض قائمة المواعيد
                return ListView.builder(
                  itemCount: filteredAppointments.length,
                  itemBuilder: (context, index) {
                    return AppointmentCard(appointment: filteredAppointments[index]);
                  },
                );
              }
              // حالة غير متوقعة
              return const Center(child: Text('حالة غير معروفة.'));
            },
          ),
        ),
      ],
    );
  }

  /// بناء شرائح الفلتر (اليوم، الأسبوع، الشهر).
  Widget _buildFilterChips() {
    final List<String> periods = ['اليوم', 'الأسبوع', 'الشهر'];
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: periods.map((period) {
          final isSelected = _filterPeriod == period;
          return ChoiceChip(
            label: Text(period),
            selected: isSelected,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _filterPeriod = period;
                });
              }
            },
            selectedColor: Theme.of(context).primaryColor.withOpacity(0.8),
            labelStyle: TextStyle(
              color: isSelected ? Colors.white : Colors.black,
            ),
            backgroundColor: Colors.grey[200],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(color: isSelected ? Colors.transparent : Colors.grey[300]!)
            ),
          );
        }).toList(),
      ),
    );
  }

  /// تطبيق الفلترة على قائمة المواعيد بناءً على الفترة المحددة.
  List<AppointmentModel> _filterAppointments(List<AppointmentModel> appointments) {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    switch (_filterPeriod) {
      case 'اليوم':
        return appointments.where((a) =>
            a.dateTime.isAfter(todayStart.subtract(const Duration(microseconds: 1))) &&
            a.dateTime.isBefore(todayEnd)).toList();
      case 'الأسبوع':
        final weekStart = todayStart.subtract(Duration(days: now.weekday - 1)); // Assuming Monday is start
        final weekEnd = weekStart.add(const Duration(days: 7));
        return appointments.where((a) =>
            a.dateTime.isAfter(weekStart.subtract(const Duration(microseconds: 1))) &&
            a.dateTime.isBefore(weekEnd)).toList();
      case 'الشهر':
        final monthStart = DateTime(now.year, now.month, 1);
        final nextMonthStart = DateTime(now.year, now.month + 1, 1);
        return appointments.where((a) =>
            a.dateTime.isAfter(monthStart.subtract(const Duration(microseconds: 1))) &&
            a.dateTime.isBefore(nextMonthStart)).toList();
      default:
        return appointments;
    }
  }
}

