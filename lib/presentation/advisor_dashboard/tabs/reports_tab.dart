// import 'package:flutter/material.dart';
// import 'package:agriculture/core/constants/colors.dart'; // Using placeholder colors
//
// // Placeholder data - replace with actual data aggregation and analysis
// class ReportData {
//   final int totalConsultations;
//   final double rating;
//   final int avgResponseTimeMinutes;
//   final double resolutionRatePercent;
//   final Map<String, double> commonIssuesPercent;
//
//   ReportData({
//     required this.totalConsultations,
//     required this.rating,
//     required this.avgResponseTimeMinutes,
//     required this.resolutionRatePercent,
//     required this.commonIssuesPercent,
//   });
// }
//
// class ReportsTab extends StatelessWidget {
//   const ReportsTab({super.key});
//
//   // Fetch or calculate report data here
//   final ReportData _reportData = ReportData(
//     totalConsultations: 45,
//     rating: 4.8,
//     avgResponseTimeMinutes: 12,
//     resolutionRatePercent: 92.0,
//     commonIssuesPercent: {
//       'آفات الطماطم': 25.0,
//       'مشاكل الري': 20.0,
//       'أمراض القمح': 15.0,
//       'نقص التسميد': 10.0,
//       'أخرى': 30.0, // Ensure percentages add up or handle 'other'
//     },
//   );
//
//   @override
//   Widget build(BuildContext context) {
//     // Sort common issues by percentage, descending
//     final sortedIssues = _reportData.commonIssuesPercent.entries.toList()
//       ..sort((a, b) => b.value.compareTo(a.value));
//
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             '📈 أداء هذا الشهر',
//             style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
//           ),
//           const SizedBox(height: 16),
//           _buildPerformanceCard(context),
//           const SizedBox(height: 24),
//           Text(
//             '📋 أكثر المشاكل شيوعاً:',
//             style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
//           ),
//           const SizedBox(height: 12),
//           _buildCommonIssuesList(context, sortedIssues),
//           const SizedBox(height: 24),
//           _buildActionButtons(context),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildPerformanceCard(BuildContext context) {
//     return Card(
//       elevation: 2,
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           children: [
//             _buildStatRow(context, Icons.chat_bubble_outline, 'إجمالي الاستشارات', '${_reportData.totalConsultations} (↑15%)'), // Placeholder for trend
//             const Divider(),
//             _buildStatRow(context, Icons.star_border, 'متوسط التقييم', '${_reportData.rating}/5'),
//             const Divider(),
//             _buildStatRow(context, Icons.timer_outlined, 'متوسط وقت الرد', '${_reportData.avgResponseTimeMinutes} دقيقة'),
//             const Divider(),
//             _buildStatRow(context, Icons.check_circle_outline, 'معدل الحل', '${_reportData.resolutionRatePercent.toStringAsFixed(0)}%'),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildStatRow(BuildContext context, IconData icon, String label, String value) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 8.0),
//       child: Row(
//         children: [
//           Icon(icon, color: Theme.of(context).primaryColor, size: 20),
//           const SizedBox(width: 12),
//           Text(label, style: Theme.of(context).textTheme.bodyLarge),
//           const Spacer(),
//           Text(value, style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildCommonIssuesList(BuildContext context, List<MapEntry<String, double>> issues) {
//     return Card(
//       elevation: 2,
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           children: issues.take(3).map((entry) { // Show top 3 as per description
//             int index = issues.indexOf(entry);
//             return Padding(
//               padding: const EdgeInsets.symmetric(vertical: 6.0),
//               child: Row(
//                 children: [
//                   Text('${index + 1}.'),
//                   const SizedBox(width: 8),
//                   Expanded(child: Text(entry.key, style: Theme.of(context).textTheme.bodyLarge)),
//                   Text('${entry.value.toStringAsFixed(0)}%', style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey[700])),
//                 ],
//               ),
//             );
//           }).toList(),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildActionButtons(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//       children: [
//         OutlinedButton.icon(
//           icon: const Icon(Icons.description_outlined),
//           label: const Text('تقرير مفصل'),
//           onPressed: _viewDetailedReport,
//         ),
//         FilledButton.icon(
//           icon: const Icon(Icons.picture_as_pdf_outlined),
//           label: const Text('تصدير PDF'),
//           onPressed: _exportPdf,
//           style: FilledButton.styleFrom(backgroundColor: AppColors.primary),
//         ),
//       ],
//     );
//   }
//
//   // Placeholder action methods
//   void _viewDetailedReport() {
//     // Implement navigation to detailed report screen
//   }
//
//   void _exportPdf() {
//     // Implement PDF export functionality
//   }
// }
//

import 'package:flutter/material.dart';
import 'package:agriculture/core/constants/colors.dart';
//عدلت زيل تعليق
class ReportsTab extends StatelessWidget {
   ReportsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final reportData = _generateReportData(); // دالة مساعدة لإنشاء بيانات الاختبار

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📈 أداء هذا الشهر',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          _buildPerformanceCard(context, reportData),
           SizedBox(height: 24),
          Text(
            '📋 أكثر المشاكل شيوعاً:',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
           SizedBox(height: 12),
          _buildCommonIssuesList(context, reportData.commonIssuesPercent),
           SizedBox(height: 24),
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildPerformanceCard(BuildContext context, ReportData data) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildStatRow(
              context,
              Icons.chat_bubble_outline,
              'إجمالي الاستشارات',
              '${data.totalConsultations}',
            ),
            const Divider(),
            _buildStatRow(
              context,
              Icons.star_border,
              'متوسط التقييم',
              '${data.rating}/5',
            ),
            const Divider(),
            _buildStatRow(
              context,
              Icons.timer_outlined,
              'متوسط وقت الرد',
              '${data.avgResponseTimeMinutes} دقيقة',
            ),
            const Divider(),
            _buildStatRow(
              context,
              Icons.check_circle_outline,
              'معدل الحل',
              '${data.resolutionRatePercent.toStringAsFixed(0)}%',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(
      BuildContext context,
      IconData icon,
      String label,
      String value,
      ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColor, size: 20),
           SizedBox(width: 12),
          Text(label, style: Theme.of(context).textTheme.bodyLarge),
           Spacer(),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommonIssuesList(
      BuildContext context,
      Map<String, double> issues,
      ) {
    final sortedIssues = issues.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: sortedIssues.take(3).map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Row(
                children: [
                  Text('${sortedIssues.indexOf(entry) + 1}.'),
                   SizedBox(width: 8),
                  Expanded(child: Text(entry.key)),
                  Text(
                    '${entry.value.toStringAsFixed(0)}%',
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        OutlinedButton.icon(
          icon: const Icon(Icons.description_outlined),
          label:  Text('تقرير مفصل'),
          onPressed: () {},
        ),
        FilledButton.icon(
          icon:  Icon(Icons.picture_as_pdf_outlined),
          label:  Text('تصدير PDF'),
          onPressed: () {},
          style: FilledButton.styleFrom(
            backgroundColor: AppColors.primary,
          ),
        ),
      ],
    );
  }

  // نموذج بيانات الاختبار
  ReportData _generateReportData() {
    return ReportData(
      totalConsultations: 45,
      rating: 4.8,
      avgResponseTimeMinutes: 12,
      resolutionRatePercent: 92.0,
      commonIssuesPercent: {
        'آفات الطماطم': 25.0,
        'مشاكل الري': 20.0,
        'أمراض القمح': 15.0,
        'نقص التسميد': 10.0,
        'أخرى': 30.0,
      },
    );
  }
}

// نموذج بيانات التقرير
class ReportData {
  final int totalConsultations;
  final double rating;
  final int avgResponseTimeMinutes;
  final double resolutionRatePercent;
  final Map<String, double> commonIssuesPercent;

  ReportData({
    required this.totalConsultations,
    required this.rating,
    required this.avgResponseTimeMinutes,
    required this.resolutionRatePercent,
    required this.commonIssuesPercent,
  });
}
