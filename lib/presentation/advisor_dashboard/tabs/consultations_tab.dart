import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/bloc/advisor_cubit.dart';
import 'package:agriculture/bloc/advisor_state.dart';
import 'package:agriculture/presentation/advisor_dashboard/components/consultation_card.dart';
import 'package:agriculture/data/models/consultation.dart'; // لاستخدام النموذج

/// تبويب عرض الاستشارات للمرشد.
/// يستخدم AdvisorCubit لجلب وعرض قائمة الاستشارات.
class ConsultationsTab extends StatefulWidget {
  const ConsultationsTab({super.key});

  @override
  State<ConsultationsTab> createState() => _ConsultationsTabState();
}

class _ConsultationsTabState extends State<ConsultationsTab> {
  // حالة محلية للبحث والتصفية
  String _searchQuery = '';
  String _filterStatus = 'الكل'; // فلتر الحالة الافتراضي

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBarAndFilter(),
        Expanded(
          // استخدام BlocBuilder للاستماع إلى حالات AdvisorCubit
          child: BlocBuilder<AdvisorCubit, AdvisorState>(
            builder: (context, state) {
              // عرض مؤشر التحميل أثناء جلب البيانات
              if (state is AdvisorLoading || state is AdvisorInitial) {
                return const Center(child: CircularProgressIndicator());
              }
              // عرض رسالة خطأ في حالة الفشل
              if (state is AdvisorError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'خطأ: ${state.message}\nيرجى التأكد من تهيئة Firebase والمحاولة مرة أخرى.',
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }
              // عرض البيانات بعد تحميلها بنجاح
              if (state is AdvisorLoaded) {
                // تطبيق الفلاتر المحلية على البيانات المحملة
                final filteredConsultations = _filterConsultations(state.consultations);

                // عرض رسالة إذا لم تكن هناك استشارات مطابقة
                if (filteredConsultations.isEmpty) {
                  return const Center(child: Text('لا توجد استشارات تطابق البحث أو الفلتر الحالي.'));
                }

                // عرض قائمة الاستشارات باستخدام ListView.builder
                return ListView.builder(
                  itemCount: filteredConsultations.length,
                  itemBuilder: (context, index) {
                    return ConsultationCard(consultation: filteredConsultations[index]);
                  },
                );
              }
              // حالة غير متوقعة (احتياطي)
              return const Center(child: Text('حالة غير معروفة.'));
            },
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث وفلتر الحالة.
  Widget _buildSearchBarAndFilter() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          // حقل البحث
          Expanded(
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'ابحث في الاستشارات...', // نص مساعد
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),
          const SizedBox(width: 8),
          // زر الفلتر (قائمة منسدلة)
          _buildFilterDropdown(),
        ],
      ),
    );
  }

  /// بناء القائمة المنسدلة لفلتر الحالة.
  Widget _buildFilterDropdown() {
    // قائمة الحالات الممكنة للفلترة
    final List<String> statuses = ['الكل', 'جديد', 'معلق', 'عاجل', 'مكتمل'];

    return DropdownButton<String>(
      value: _filterStatus,
      icon: const Icon(Icons.filter_list),
      underline: Container(), // إزالة الخط السفلي الافتراضي
      onChanged: (String? newValue) {
        if (newValue != null) {
          setState(() {
            _filterStatus = newValue;
          });
        }
      },
      items: statuses.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
    );
  }

  /// تطبيق البحث والفلترة على قائمة الاستشارات.
  List<ConsultationModel> _filterConsultations(List<ConsultationModel> consultations) {
    List<ConsultationModel> filteredList = consultations;

    // تطبيق فلتر الحالة
    if (_filterStatus != 'الكل') {
      filteredList = filteredList.where((c) => c.status == _filterStatus).toList();
    }

    // تطبيق البحث النصي
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filteredList = filteredList.where((c) {
        return c.userName.toLowerCase().contains(query) ||
               c.cropType.toLowerCase().contains(query) ||
               c.problemDescription.toLowerCase().contains(query);
      }).toList();
    }

    return filteredList;
  }
}

