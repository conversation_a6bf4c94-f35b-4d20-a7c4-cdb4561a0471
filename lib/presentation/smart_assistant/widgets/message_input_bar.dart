import 'package:flutter/material.dart';

/// ويدجت شريط الإدخال السفلي لإرسال الرسائل وإرفاق الصور.
class MessageInputBar extends StatelessWidget {
  final TextEditingController textController;
  final Function(String) onSendMessage;
  final VoidCallback onAttachImage;

  const MessageInputBar({
    super.key,
    required this.textController,
    required this.onSendMessage,
    required this.onAttachImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4.0,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر إرفاق صورة
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: onAttachImage,
            tooltip: 'إرفاق صورة',
            color: Theme.of(context).primaryColor,
          ),
          // حقل إدخال النص
          Expanded(
            child: TextField(
              controller: textController,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالتك هنا...', // نص مساعد
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
              ),
              textInputAction: TextInputAction.send, // تغيير زر الإدخال إلى إرسال
              onSubmitted: onSendMessage, // إرسال عند الضغط على زر الإدخال
              minLines: 1,
              maxLines: 5, // السماح بعدة أسطر
            ),
          ),
          // زر الإرسال
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: () => onSendMessage(textController.text),
            tooltip: 'إرسال',
            color: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }
}

