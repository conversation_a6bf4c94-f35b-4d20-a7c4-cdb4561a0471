import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // لتنسيق الوقت

/// ويدجت لعرض فقاعة رسالة واحدة في المحادثة.
class MessageBubble extends StatelessWidget {
  final String message;
  final bool isMe;
  final String? imageUrl; // مسار الصورة (اختياري)
  final DateTime timestamp;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.imageUrl,
    required this.timestamp,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final alignment = isMe ? Alignment.centerRight : Alignment.centerLeft;
    final bubbleColor = isMe ? theme.primaryColorLight : Colors.grey[200];
    final textColor = isMe ? Colors.black87 : Colors.black87;
    final timeColor = isMe ? Colors.black54 : Colors.black54;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      alignment: alignment,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 14.0),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75, // حد أقصى لعرض الفقاعة
        ),
        decoration: BoxDecoration(
          color: bubbleColor,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(12.0),
            topRight: const Radius.circular(12.0),
            bottomLeft: isMe ? const Radius.circular(12.0) : const Radius.circular(0),
            bottomRight: isMe ? const Radius.circular(0) : const Radius.circular(12.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2.0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            // عرض الصورة إذا كانت موجودة
            if (imageUrl != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                // TODO: عرض الصورة بشكل أفضل (قد تحتاج لحزمة مثل cached_network_image إذا كانت من الشبكة)
                // حاليًا يفترض أنها مسار محلي
                child: Image.asset(imageUrl!, fit: BoxFit.contain), // أو Image.file إذا كان مسار ملف
              ),
            // عرض النص
            if (message.isNotEmpty)
              Text(
                message,
                style: TextStyle(color: textColor, fontSize: 15),
              ),
            const SizedBox(height: 4.0),
            // عرض الوقت
            Text(
              DateFormat('hh:mm a').format(timestamp), // تنسيق الوقت (e.g., 10:30 AM)
              style: TextStyle(color: timeColor, fontSize: 11),
            ),
          ],
        ),
      ),
    );
  }
}

