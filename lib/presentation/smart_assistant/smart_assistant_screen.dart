import "../../core/constants/app_colors.dart";
import "./cubit/smart_assistant_cubit.dart"; // <-- تم التعديل: مسار نسبي
import "./cubit/smart_assistant_state.dart"; // <-- تم التعديل: مسار نسبي
import "./widgets/message_bubble.dart"; // <-- تم التعديل: مسار نسبي
import "./widgets/message_input_bar.dart"; // <-- تم التعديل: مسار نسبي
import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";
import "package:image_picker/image_picker.dart";
//عدلت زيل تعليق
/// شاشة المحادثة مع المرشد الذكي.
class SmartAssistantScreen extends StatefulWidget {
  const SmartAssistantScreen({super.key});

  @override
  State<SmartAssistantScreen> createState() => _SmartAssistantScreenState();
}

class _SmartAssistantScreenState extends State<SmartAssistantScreen> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController(); // للتحكم في التمرير

  @override
  void initState() {
    super.initState();
    // يمكنك إضافة رسالة ترحيب أولية هنا إذا أردت
    // context.read<SmartAssistantCubit>().addInitialMessage();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// دالة لإرسال رسالة نصية.
  void _sendMessage(String text) {
    if (text.trim().isNotEmpty) {
      context.read<SmartAssistantCubit>().sendMessage(text.trim());
      _textController.clear();
      _scrollToBottom(); // تمرير للأسفل بعد إرسال الرسالة
    }
  }

  /// دالة لإرسال صورة.
  Future<void> _sendImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery); // أو ImageSource.camera
    if (pickedFile != null) {
      context.read<SmartAssistantCubit>().sendImage(pickedFile.path);
      _scrollToBottom(); // تمرير للأسفل بعد إرسال الصورة
    }
  }

  /// دالة لطلب نصيحة موسمية.
  void _getSeasonalAdvice() {
    context.read<SmartAssistantCubit>().getSeasonalAdvice();
    _scrollToBottom();
  }

  /// دالة لطلب التحويل لمرشد بشري.
  void _requestHumanAdvisor() {
    context.read<SmartAssistantCubit>().requestHumanAdvisor();
    _scrollToBottom();
  }

  /// دالة للتمرير إلى أسفل القائمة.
  void _scrollToBottom() {
    // استخدام postFrameCallback للتأكد من أن الواجهة قد تم بناؤها قبل التمرير
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("المرشد الذكي"),
        actions: [
          // زر طلب نصيحة موسمية
          IconButton(
            icon: const Icon(Icons.wb_sunny_outlined),
            tooltip: "نصيحة موسمية",
            onPressed: _getSeasonalAdvice,
          ),
          // زر التحويل لمرشد بشري
          IconButton(
            icon: const Icon(Icons.support_agent_outlined),
            tooltip: "التحدث لمرشد بشري",
            onPressed: _requestHumanAdvisor,
          ),
        ],
      ),
      body: BlocConsumer<SmartAssistantCubit, SmartAssistantState>(
        listener: (context, state) {
          // تمرير للأسفل عند وصول رسالة جديدة
          // نتحقق من أن الحالة ليست Loading لتجنب التمرير أثناء التحميل
          if (state is SmartAssistantLoaded || state is SmartAssistantMessageReceived) {
             _scrollToBottom();
          }
          // يمكنك إضافة رسائل Snackbar هنا للأخطاء أو التأكيدات
          if (state is SmartAssistantError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("خطأ: ${state.message}")),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // *** إصلاح خطأ Overflow: استخدام Expanded للقائمة ***
              Expanded(
                child: ListView.builder(
                  controller: _scrollController, // ربط الـ ScrollController
                  padding: const EdgeInsets.all(8.0),
                  itemCount: state.messages.length,
                  itemBuilder: (context, index) {
                    final message = state.messages[index];
                    return MessageBubble(
                      message: message.text,
                      isMe: message.isUserMessage, // <-- تم التعديل: استخدام isUserMessage
                      imageUrl: message.imageUrl,
                      timestamp: message.timestamp,
                    );
                  },
                ),
              ),
              // عرض مؤشر التحميل إذا كان الروبوت يفكر
              if (state is SmartAssistantLoading)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: LinearProgressIndicator(),
                ),
              // شريط إدخال الرسالة
              MessageInputBar(
                textController: _textController,
                onSendMessage: _sendMessage,
                onAttachImage: _sendImage,
              ),
            ],
          );
        },
      ),
    );
  }
}



class ReportData {
  final int totalConsultations;
  final double rating;
  final int avgResponseTimeMinutes;
  final double resolutionRatePercent;
  final Map<String, double> commonIssuesPercent;

  const ReportData({
    required this.totalConsultations,
    required this.rating,
    required this.avgResponseTimeMinutes,
    required this.resolutionRatePercent,
    required this.commonIssuesPercent,
  });
}

class ReportsTab extends StatelessWidget {
   ReportsTab({super.key});

  @override
  Widget build(BuildContext context) {
    // نقل البيانات إلى داخل دالة البناء لجعلها غير ثابتة
    final reportData = ReportData(
      totalConsultations: 45,
      rating: 4.8,
      avgResponseTimeMinutes: 12,
      resolutionRatePercent: 92.0,
      commonIssuesPercent: {
        'آفات الطماطم': 25.0,
        'مشاكل الري': 20.0,
        'أمراض القمح': 15.0,
        'نقص التسميد': 10.0,
        'أخرى': 30.0,
      },
    );

    final sortedIssues = reportData.commonIssuesPercent.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📈 أداء هذا الشهر',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
           SizedBox(height: 16),
          _buildPerformanceCard(context, reportData),
           SizedBox(height: 24),
          Text(
            '📋 أكثر المشاكل شيوعاً:',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
           SizedBox(height: 12),
          _buildCommonIssuesList(context, sortedIssues),
           SizedBox(height: 24),
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildPerformanceCard(BuildContext context, ReportData reportData) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildStatRow(context, Icons.chat_bubble_outline, 'إجمالي الاستشارات',
                '${reportData.totalConsultations} (↑15%)'),
             Divider(),
            _buildStatRow(
                context, Icons.star_border, 'متوسط التقييم', '${reportData.rating}/5'),
             Divider(),
            _buildStatRow(context, Icons.timer_outlined, 'متوسط وقت الرد',
                '${reportData.avgResponseTimeMinutes} دقيقة'),
             Divider(),
            _buildStatRow(context, Icons.check_circle_outline, 'معدل الحل',
                '${reportData.resolutionRatePercent.toStringAsFixed(0)}%'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(
      BuildContext context, IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColor, size: 20),
           SizedBox(width: 12),
          Text(label, style: Theme.of(context).textTheme.bodyLarge),
          Spacer(),
          Text(value,
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildCommonIssuesList(
      BuildContext context, List<MapEntry<String, double>> issues) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: issues
              .take(3)
              .map((entry) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 6.0),
            child: Row(
              children: [
                Text('${issues.indexOf(entry) + 1}.'),
                SizedBox(width: 8),
                Expanded(
                    child: Text(entry.key,
                        style: Theme.of(context).textTheme.bodyLarge)),
                Text('${entry.value.toStringAsFixed(0)}%',
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge
                        ?.copyWith(color: Colors.grey[700])),
              ],
            ),
          ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        OutlinedButton.icon(
          icon:  Icon(Icons.description_outlined),
          label:  Text('تقرير مفصل'),
          onPressed: _viewDetailedReport,
        ),
        FilledButton.icon(
          icon:  Icon(Icons.picture_as_pdf_outlined),
          label:  Text('تصدير PDF'),
          onPressed: _exportPdf,
          style: FilledButton.styleFrom(backgroundColor: AppColors.primary),
        ),
      ],
    );
  }

  void _viewDetailedReport() {
    // TODO: Implement detailed report view
  }

  void _exportPdf() {
    // TODO: Implement PDF export functionality
  }
}

