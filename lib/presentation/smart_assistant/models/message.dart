/// نموذج يمثل رسالة واحدة في المحادثة.
class Message {
  final String id; // معرف فريد للرسالة
  final String text;
  final String? imageUrl; // رابط الصورة (إذا كانت الرسالة صورة)
  final DateTime timestamp;
  final bool isUserMessage; // هل الرسالة من المستخدم أم من الروبوت؟

  Message({
    required this.id,
    required this.text,
    this.imageUrl,
    required this.timestamp,
    required this.isUserMessage,
  });
}

