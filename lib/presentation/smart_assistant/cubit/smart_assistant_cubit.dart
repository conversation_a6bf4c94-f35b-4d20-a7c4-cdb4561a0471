import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart'; // لإنشاء معرفات فريدة للرسائل
import 'smart_assistant_state.dart'; // <-- تم التعديل: مسار نسبي
import '../models/message.dart'; // <-- تم التعديل: مسار نسبي
import '../../../services/dialogflow_service.dart'; // <-- تم التعديل: مسار نسبي
//عدلت زيل تعليق
/// الـ Cubit المسؤول عن إدارة حالة شاشة المرشد الذكي.
class SmartAssistantCubit extends Cubit<SmartAssistantState> {
  // خدمة Dialogflow (يجب حقنها أو تهيئتها بشكل صحيح)
  final DialogflowService _dialogflowService = DialogflowService();
  // لإنشاء معرفات فريدة
  final Uuid _uuid = const Uuid();

  /// الباني الافتراضي، يبدأ بالحالة الأولية مع رسالة ترحيب.
  SmartAssistantCubit() : super(const SmartAssistantInitial()) {
    // إضافة رسالة ترحيب أولية من الروبوت
    _addBotMessage("مرحباً بك! أنا المرشد الذكي. كيف يمكنني مساعدتك اليوم؟ 🤖");
  }

  /// إضافة رسالة جديدة إلى قائمة الرسائل وتحديث الحالة.
  void _addMessage(Message message) {
    final currentState = state;
    // التأكد من أن الحالة الحالية تحتوي على قائمة رسائل قابلة للتعديل
    final currentMessages = currentState.messages;
    final updatedMessages = List<Message>.from(currentMessages)..add(message);
    emit(SmartAssistantLoaded(messages: updatedMessages));
  }

  /// إضافة رسالة من الروبوت.
  void _addBotMessage(String text) {
    final botMessage = Message(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
      isUserMessage: false,
    );
    _addMessage(botMessage);
  }

  /// إرسال رسالة نصية من المستخدم إلى الروبوت.
  Future<void> sendMessage(String text) async {
    // 1. إضافة رسالة المستخدم فورًا إلى الواجهة
    final userMessage = Message(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
      isUserMessage: true,
    );
    // إضافة رسالة المستخدم إلى الحالة الحالية قبل إرسالها للخدمة
    final messagesBeforeLoading = List<Message>.from(state.messages)..add(userMessage);
    emit(SmartAssistantLoading(messages: messagesBeforeLoading));

    try {
      // 2. إرسال نص المستخدم إلى خدمة Dialogflow للحصول على الرد
      final botReplyText = await _dialogflowService.sendQuery(text);

      // 3. إضافة رد الروبوت إلى الواجهة
      _addBotMessage(botReplyText);

    } catch (e) {
      print('خطأ في SmartAssistantCubit عند إرسال الرسالة: $e');
      // 4. في حالة الخطأ، إضافة رسالة خطأ من الروبوت
      _addBotMessage("عذرًا، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.");
      // يمكن أيضًا إصدار حالة خطأ منفصلة إذا أردت التعامل معها بشكل مختلف في الواجهة
      // emit(SmartAssistantError(message: e.toString(), messages: state.messages));
    }
  }

  // --- وظائف مستقبلية (تحتاج إلى إكمال) ---

  /// إرسال رسالة تحتوي على صورة (قيد التطوير).
  Future<void> sendImage(String imagePath) async {
    // TODO: إضافة منطق إرسال الصورة وتحليلها
    // 1. إضافة رسالة المستخدم (مع الصورة) إلى الواجهة
    final userMessage = Message(
      id: _uuid.v4(),
      text: 'تم إرفاق صورة.', // نص مؤقت
      imageUrl: imagePath, // مسار الصورة
      timestamp: DateTime.now(),
      isUserMessage: true,
    );
    final messagesBeforeLoading = List<Message>.from(state.messages)..add(userMessage);
    emit(SmartAssistantLoading(messages: messagesBeforeLoading));

    // 2. إرسال الصورة إلى خدمة تحليل الصور (مثال)
    // final analysisResult = await ImageAnalysisService().analyzeImage(imagePath);
    // 3. الحصول على التشخيص أو الرد
    // final botReplyText = "نتائج تحليل الصورة: $analysisResult";
    final botReplyText = "شكرًا على إرسال الصورة، ما زلت أتعلم كيفية تحليلها."; // رد مؤقت

    // 4. إضافة رد الروبوت
    _addBotMessage(botReplyText);
    print("إرسال صورة: $imagePath (قيد التطوير)");
  }

  /// طلب نصيحة موسمية (قيد التطوير).
  Future<void> getSeasonalAdvice() async {
    // TODO: إضافة منطق جلب النصائح الموسمية (قد يعتمد على الموقع)
    print("طلب نصيحة موسمية (قيد التطوير)");
    _addBotMessage("النصائح الموسمية قيد التطوير. بشكل عام، تأكد من الري المنتظم في الصيف!");
  }

  /// طلب التحويل إلى مرشد بشري (قيد التطوير).
  void requestHumanAdvisor() {
    // TODO: إضافة منطق بدء محادثة مع مرشد بشري
    print("طلب التحويل لمرشد بشري (قيد التطوير)");
    _addBotMessage("جاري محاولة توصيلك بمرشد بشري...");
    // هنا يمكنك الانتقال إلى شاشة أخرى أو إرسال إشعار للمرشد البشري
  }
}

