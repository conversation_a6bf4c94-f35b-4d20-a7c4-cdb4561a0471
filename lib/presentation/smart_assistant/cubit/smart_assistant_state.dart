import 'package:equatable/equatable.dart';
import '../models/message.dart'; // <-- تم التعديل: مسار نسبي

/// الحالة الأساسية لـ SmartAssistantCubit.
abstract class SmartAssistantState extends Equatable {
  final List<Message> messages;

  const SmartAssistantState({this.messages = const []});

  @override
  List<Object> get props => [messages];
}

/// الحالة الأولية قبل بدء أي محادثة أو تحميل.
class SmartAssistantInitial extends SmartAssistantState {
  const SmartAssistantInitial() : super(messages: const []);
}

/// حالة جاري التحميل (مثل انتظار رد الروبوت).
class SmartAssistantLoading extends SmartAssistantState {
  const SmartAssistantLoading({required List<Message> messages}) : super(messages: messages);
}

/// حالة تم تحميل البيانات بنجاح (عرض الرسائل).
class SmartAssistantLoaded extends SmartAssistantState {
  const SmartAssistantLoaded({required List<Message> messages}) : super(messages: messages);
}

/// حالة وصول رسالة جديدة (يمكن استخدامها لتشغيل التمرير التلقائي).
class SmartAssistantMessageReceived extends SmartAssistantState {
  const SmartAssistantMessageReceived({required List<Message> messages}) : super(messages: messages);
}

/// حالة حدوث خطأ.
class SmartAssistantError extends SmartAssistantState {
  final String message;

  const SmartAssistantError({required this.message, required List<Message> messages}) : super(messages: messages);

  @override
  List<Object> get props => [message, messages];
}

