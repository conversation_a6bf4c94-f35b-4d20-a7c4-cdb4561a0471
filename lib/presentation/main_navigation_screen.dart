import 'package:agriculture/core/constants/app_strings.dart';
// import 'package:agriculture/core/constants/strings.dart'; // <-- تم التعليق/الحذف: استيراد مكرر أو غير صحيح
import 'package:agriculture/presentation/main_screens/appointments_screen.dart';
import 'package:agriculture/presentation/main_screens/consultations_screen.dart';
import 'package:agriculture/presentation/main_screens/plant_monitoring_screen.dart';
import 'package:agriculture/presentation/main_screens/reports_screen.dart';
import 'package:agriculture/presentation/smart_assistant/cubit/smart_assistant_cubit.dart';
import 'package:agriculture/presentation/smart_assistant/smart_assistant_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// الشاشة الرئيسية التي تحتوي على شريط التنقل السفلي وتدير عرض الشاشات الرئيسية.
class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _selectedIndex = 0; // الفهرس الافتراضي للشاشة الأولى (الاستشارات)

  // قائمة الشاشات الرئيسية التي يتم التنقل بينها
  static const List<Widget> _widgetOptions = <Widget>[
    ConsultationsScreen(),
    AppointmentsScreen(),
    PlantMonitoringScreen(),
    ReportsScreen(),
  ];

  /// دالة لتغيير الشاشة المعروضة عند النقر على عنصر في شريط التنقل.
  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // يتم تحديد اتجاه النص تلقائيًا بناءً على لغة التطبيق (العربية هنا)
    // وبالتالي سيتم ترتيب العناصر في BottomNavigationBar من اليمين لليسار.
    return Scaffold(
      // الجسم يعرض الشاشة المحددة حاليًا من القائمة
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      // شريط التنقل السفلي
      bottomNavigationBar: BottomNavigationBar(
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            label: AppStrings.consultationsTab, // استشارات
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today_outlined),
            label: AppStrings.appointmentsTab, // مواعيد
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.eco_outlined),
            label: AppStrings.plantMonitoringTab, // مراقبة
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart_outlined),
            label: AppStrings.reportsTab, // تقارير
          ),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed, // لإظهار جميع العناوين دائمًا
        // الألوان تم تحديدها في الـ Theme بملف main.dart
      ),
      // زر عائم للمرشد الذكي (اختياري، يمكن وضعه في AppBar أيضًا)
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => BlocProvider(
                create: (context) => SmartAssistantCubit(), // توفير Cubit المرشد الذكي
                child: const SmartAssistantScreen(),
              ),
            ),
          );
        },
        tooltip: 'المرشد الذكي',
        child: const Icon(Icons.smart_toy_outlined),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked, // لوضعه في المنتصف
      // إضافة مساحة للزر العائم في شريط التنقل (إذا كان شريط التنقل من النوع المزود بفتحة)
      // bottomNavigationBar: BottomAppBar(
      //   shape: const CircularNotchedRectangle(),
      //   notchMargin: 6.0,
      //   child: BottomNavigationBar(...),
      // ),
    );
  }
}
