# مشروع الزراعة

## هيكل المشروع الجديد

تم إعادة هيكلة المشروع وفقًا لمبادئ التصميم النظيف (Clean Architecture) لتحسين قابلية الصيانة والاختبار والتوسع.

### الهيكل الجديد

```
lib/
├── core/                  # المكونات الأساسية المشتركة
│   ├── constants/         # الثوابت (ألوان، نصوص...)
│   ├── errors/            # معالجة الأخطاء
│   ├── network/           # إعدادات الشبكة العامة
│   ├── utils/             # أدوات مساعدة
│   └── widgets/           # الويدجت المشتركة
│
├── data/                  # طبقة البيانات
│   ├── datasources/       # مصادر البيانات
│   │   ├── local/         # مصادر البيانات المحلية
│   │   └── remote/        # مصادر البيانات البعيدة
│   ├── models/            # نماذج البيانات
│   └── repositories/      # تنفيذات المستودعات
│
├── domain/                # طبقة المنطق التجاري
│   ├── entities/          # الكيانات الأساسية
│   ├── repositories/      # واجهات المستودعات
│   └── usecases/          # حالات الاستخدام
│
├── presentation/         # طبقة العرض
│   ├── bloc/             # مديرو الحالة (BLoC/Cubit)
│   ├── pages/            # صفحات التطبيق
│   └── widgets/          # الويدجت الخاصة بالعرض
│
└── main.dart             # نقطة الدخول للتطبيق
```

### فوائد الهيكل الجديد

1. **الفصل الواضح للمسؤوليات**: كل طبقة لها مسؤولية محددة.
2. **قابلية الاختبار**: يمكن اختبار كل طبقة بشكل منفصل.
3. **قابلية التوسع**: سهولة إضافة ميزات جديدة دون التأثير على الميزات الموجودة.
4. **قابلية الصيانة**: سهولة فهم وصيانة الكود.
5. **العمل الجماعي**: يمكن لعدة مطورين العمل على ميزات مختلفة في نفس الوقت.


### الميزات التي تم تحديثها

1. ✅ المصادقة (تسجيل الدخول والتسجيل)
2. ✅ الصفحة الرئيسية
3. ✅ الطقس
4. ✅ المحاصيل الزراعية
5. ✅ المعالم الزراعية
6. ✅ المنتدى المجتمعي
7. ✅ التعليم والتدريب
8. ✅ الدعم الحكومي
9. ✅ تسويق المنتجات
10. ✅ الآفات والأمراض
11. ✅ التواصل مع المهندس
# مشروع تطبيق الزراعة

## نظرة عامة
هذا المشروع هو تطبيق للزراعة يهدف إلى تقديم خدمات متنوعة للمزارعين والمهتمين بالزراعة. يشمل التطبيق معلومات عن الطقس، المحاصيل الزراعية، المعالم الزراعية، التعليم الزراعي، الخدمات الحكومية، وتسويق المنتجات الزراعية.

## هيكل المشروع

### المجلدات الرئيسية
- **lib**: المجلد الرئيسي للكود المصدري
  - **core**: يحتوي على المكونات الأساسية للتطبيق
  - **data**: يحتوي على طبقة البيانات
  - **domain**: يحتوي على طبقة المنطق التجاري
  - **presentation**: يحتوي على واجهة المستخدم
  - **routing**: يحتوي على التنقل بين الصفحات

## تفضيلات المشروع وإرشادات التطوير

### 1. استخدام اللغة العربية
- استخدام اللغة العربية في التواصل والتوثيق والتعليقات
- استخدام التعليقات باللغة العربية في الكود والملفات المشتركة
- هذا يسهل فهم الكود للمطورين العرب ويحافظ على اتساق المشروع
- يجب أن تكون جميع وثائق المشروع والتعليمات باللغة العربية

### 2. تحديث ملف الاستيرادات بانتظام
- عند إضافة ملفات جديدة، يجب إضافتها إلى المكان المناسب في ملف `imports.dart`
- الحفاظ على التنظيم المنطقي للاستيرادات حسب المجموعات المحددة
- يحتوي ملف `imports.dart` على جميع المسارات المنظمة حسب الفئات التالية:
  - **Core**: الثوابت، التهيئة، السمات، الأخطاء، الأدوات المساعدة
  - **Data**: مصادر البيانات المحلية والبعيدة، المستودعات، نماذج البيانات
  - **Domain**: الكيانات، واجهات المستودعات، حالات الاستخدام
  - **Presentation**: مكتبات إدارة الحالة، الصفحات، المكونات
  - **Routing**: مسارات التطبيق ومديرات التنقل

### 3. استخدام ملفات الفهرسة
- إنشاء ملفات `index.dart` في المجلدات الفرعية لتسهيل الاستيراد
- هذا يقلل من تعقيد الاستيرادات ويجعل الكود أكثر قابلية للصيانة

### 4. توثيق التغييرات
- الاحتفاظ بسجل للتغييرات التي تتم على ملفات الاستيرادات والثوابت
- توثيق سبب التغييرات لمساعدة المطورين الآخرين على فهم القرارات
- إنشاء ملف توثيق شامل للمشروع يتضمن جميع المكونات والوظائف

### 5. التحقق من الملفات المشتركة قبل إنشاء جديدة
- التأكد دائمًا من عدم وجود ثوابت أو مكونات مشابهة قبل إنشاء جديدة
- استخدام الثوابت والمكونات الموجودة لتجنب التكرار
- عند إنشاء خدمة جديدة، يجب التحقق من إمكانية استخدام الخدمات الموجودة

### 6. الالتزام بالهندسة المعمارية النظيفة
- اتباع نمط Clean Architecture مع فصل واضح بين الطبقات
- استخدام Cubit بدلاً من StatefulWidget لإدارة حالة التطبيق
- تجزئة الوظائف لتسهيل الصيانة والتطوير

### 7. التركيز على الثوابت والملفات التشاركية
- استخدام الثوابت بشكل موحد في جميع أنحاء المشروع
- تنظيم الثوابت حسب الوظيفة والاستخدام
- مراجعة الملفات التشاركية قبل إضافة أي مكونات جديدة

### 8. إدارة التحديثات والتغييرات
- عند إنشاء ملفات جديدة، يجب استبدال الملفات القديمة أو حذفها
- توثيق سبب التغيير والتحديث في ملف التوثيق
- الحفاظ على تناسق الكود بعد التحديثات

## الميزات الرئيسية

### المصادقة
- تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- تسجيل الدخول برقم الهاتف
- تسجيل الدخول بحساب Google
- إنشاء حساب جديد
- تحديث الملف الشخصي

### الطقس
- عرض حالة الطقس الحالية
- توقعات الطقس على مدار الساعة
- توقعات الطقس الأسبوعية

### المحاصيل الزراعية
- قائمة المحاصيل
- تفاصيل المحصول
- التقويم الزراعي
- أمراض المحاصيل

### المعالم الزراعية
- قائمة المعالم
- تفاصيل المعلم
- خريطة المعالم
- عرض البيانات

### منتدى المجتمع
- عرض المنشورات
- إضافة منشور جديد
- التعليق والإعجاب

## التقنيات المستخدمة

### إدارة الحالة
- Flutter Bloc/Cubit

### قواعد البيانات
- Firebase
- التخزين المحلي (Shared Preferences)

### الخدمات الخارجية
- خدمة الطقس
- Google Drive
- خدمات الموقع الجغرافي

## الثوابت والإعدادات

### ثوابت التطبيق
- معلومات التطبيق (الاسم، الإصدار، إلخ)
- روابط التطبيق (سياسة الخصوصية، شروط الخدمة، إلخ)
- إعدادات الوسائط (جودة الصور، الحد الأقصى للحجم، إلخ)
- إعدادات الشبكة (مهلة الاتصال، إلخ)

## كيفية البدء

### المتطلبات
- Flutter SDK
- Firebase حساب ومشروع
- مفاتيح API للخدمات الخارجية (الطقس، Google Drive، إلخ)

### الإعداد
1. تثبيت التبعيات: `flutter pub get`
2. إعداد Firebase
3. إعداد مفاتيح API
4. تشغيل التطبيق: `flutter run`

