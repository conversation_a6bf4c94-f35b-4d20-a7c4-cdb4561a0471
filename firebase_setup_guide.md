# دليل تهيئة Firebase لمشروع المرشد الزراعي

يهدف هذا الدليل إلى مساعدتك في ربط مشروع Flutter لتطبيق "المرشد الزراعي" مع Firebase لاستخدام Firestore كقاعدة بيانات خلفية.

**المتطلبات الأساسية:**

1.  **حساب Google:** ستحتاج إلى حساب Google لإنشاء مشروع Firebase.
2.  **مشروع Firebase:** قم بإنشاء مشروع جديد على [وحدة تحكم Firebase](https://console.firebase.google.com/).
3.  **FlutterFire CLI:** تأكد من تثبيت واجهة سطر أوامر FlutterFire. إذا لم تكن مثبتة، قم بتشغيل الأمر التالي في الطرفية (Terminal):
    ```bash
    dart pub global activate flutterfire_cli
    ```

**خطوات التهيئة:**

1.  **إضافة التبعيات إلى `pubspec.yaml`:**
    افتح ملف `pubspec.yaml` في مشروعك وأضف التبعيات التالية ضمن قسم `dependencies`:
    ```yaml
    dependencies:
      flutter:
        sdk: flutter
      # ... (التبعيات الأخرى الموجودة)

      # تبعيات Firebase الأساسية
      firebase_core: ^2.24.0 # استخدم أحدث إصدار متوافق
      cloud_firestore: ^4.14.0 # استخدم أحدث إصدار متوافق

      # تبعيات إدارة الحالة (موجودة بالفعل)
      flutter_bloc: ^8.1.3
      equatable: ^2.0.5 # تبعية لـ flutter_bloc

      # تبعيات أخرى (موجودة بالفعل)
      intl: ^0.18.0
      flutter_localizations:
        sdk: flutter
    ```
    بعد إضافة التبعيات، قم بتشغيل الأمر `flutter pub get` في الطرفية لتنزيلها.

2.  **تهيئة FlutterFire:**
    افتح الطرفية في المجلد الجذر لمشروع Flutter الخاص بك وقم بتسجيل الدخول إلى Firebase CLI (إذا لم تكن قد سجلت الدخول من قبل):
    ```bash
    firebase login
    ```
    ثم قم بتشغيل أمر التهيئة لربط مشروع Flutter بمشروع Firebase الخاص بك:
    ```bash
    flutterfire configure
    ```
    *   سيطلب منك هذا الأمر تحديد مشروع Firebase الذي أنشأته.
    *   سيقوم تلقائيًا بتسجيل تطبيقات Android و iOS (إذا كانت موجودة) في مشروع Firebase.
    *   الأهم من ذلك، سيقوم بإنشاء ملف `lib/firebase_options.dart` الذي يحتوي على إعدادات التهيئة الخاصة بمشروعك لكل منصة.

3.  **إضافة ملفات الإعداد الخاصة بالمنصات:**
    *   **Android:** سيقوم أمر `flutterfire configure` عادةً بتنزيل ملف `google-services.json` ووضعه في المسار الصحيح (`android/app/google-services.json`). تأكد من وجوده.
    *   **iOS:** سيقوم أمر `flutterfire configure` عادةً بتنزيل ملف `GoogleService-Info.plist` ووضعه في المسار الصحيح (`ios/Runner/GoogleService-Info.plist`). تأكد من وجوده وافتحه باستخدام Xcode للتأكد من إضافته إلى الهدف (Target) الصحيح.

4.  **تهيئة Firebase في `main.dart`:**
    لقد تم بالفعل إضافة الكود اللازم لتهيئة Firebase في ملف `lib/main.dart`. تأكد من إلغاء التعليق عن سطر تهيئة Firebase:
    ```dart
    // في بداية دالة main()
    WidgetsFlutterBinding.ensureInitialized();

    // تهيئة Firebase
    try {
      // *** إلغاء التعليق عن السطر التالي ***
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform, // استخدم الخيارات الافتراضية
      );
      print("Firebase تم التهيئة بنجاح!");
    } catch (e) {
      print("فشل تهيئة Firebase: $e");
      // التعامل مع الخطأ
    }

    // ... (بقية الكود في main)
    ```

5.  **إعداد قاعدة بيانات Firestore:**
    *   اذهب إلى وحدة تحكم Firebase الخاصة بمشروعك.
    *   في القائمة الجانبية، اختر "Firestore Database".
    *   انقر على "Create database".
    *   اختر البدء في **وضع الإنتاج (Production mode)** أو **وضع الاختبار (Test mode)**.
        *   **وضع الاختبار:** يسمح بالقراءة والكتابة للجميع لفترة محدودة (عادة 30 يومًا). مناسب للتطوير الأولي.
        *   **وضع الإنتاج:** يتطلب إعداد قواعد أمان (Security Rules) لتحديد من يمكنه الوصول إلى البيانات. **هذا هو الوضع الموصى به للتطبيقات الحقيقية.**
    *   اختر موقعًا لقاعدة البيانات (اختر الموقع الأقرب لمستخدميك).
    *   انقر على "Enable".

6.  **إنشاء المجموعات (Collections) في Firestore:**
    الكود الحالي يفترض وجود المجموعات التالية في Firestore:
    *   `consultations`: لتخزين مستندات الاستشارات.
    *   `appointments`: لتخزين مستندات المواعيد.
    *   `plantMonitorings`: لتخزين مستندات مراقبة النباتات.
    
    يمكنك إنشاء هذه المجموعات يدويًا من وحدة تحكم Firebase أو سيتم إنشاؤها تلقائيًا عند أول عملية كتابة تتم على مجموعة غير موجودة (إذا كانت قواعد الأمان تسمح بذلك).

**ملاحظات هامة:**

*   **قواعد الأمان (Security Rules):** في وضع الإنتاج، يجب عليك كتابة قواعد أمان قوية في Firestore لتحديد من يمكنه قراءة وكتابة البيانات. بدون قواعد مناسبة، قد تكون بياناتك غير آمنة. ابدأ بقواعد بسيطة تسمح للمستخدمين المصادق عليهم فقط بالوصول إلى بياناتهم.
*   **الفهرسة (Indexing):** قد تحتاج Firestore إلى إنشاء فهارس تلقائيًا أو يدويًا لبعض الاستعلامات المعقدة (مثل الاستعلامات التي تتضمن `where` و `orderBy` على حقول مختلفة). ستظهر لك رسائل خطأ في وحدة التحكم أو روابط لإنشاء الفهارس المطلوبة إذا لزم الأمر.
*   **التكلفة:** استخدام Firebase و Firestore له تكاليف مرتبطة به بناءً على حجم الاستخدام (القراءة، الكتابة، التخزين، إلخ). راجع صفحة تسعير Firebase لفهم التكاليف المحتملة.

باتباع هذه الخطوات، يجب أن يكون تطبيق "المرشد الزراعي" قادرًا على الاتصال بـ Firebase Firestore لجلب وتخزين البيانات.
