# توثيق مشروع تطبيق الزراعة

## نظرة عامة
هذا المستند يوفر توثيقًا شاملاً لمشروع تطبيق الزراعة، بما في ذلك هيكل المشروع، والمكونات، والوظائف، وتنظيم الاستيرادات.

## هيكل المشروع
تم تصميم المشروع وفقًا لمبادئ التصميم النظيف (Clean Architecture) لتحسين قابلية الصيانة والاختبار والتوسع.

```
lib/
├── core/                  # المكونات الأساسية المشتركة
│   ├── constants/         # الثوابت (ألوان، نصوص...)
│   ├── errors/            # معالجة الأخطاء
│   ├── network/           # إعدادات الشبكة العامة
│   ├── utils/             # أدوات مساعدة
│   └── widgets/           # الويدجت المشتركة
│
├── data/                  # طبقة البيانات
│   ├── datasources/       # مصادر البيانات
│   │   ├── local/         # مصادر البيانات المحلية
│   │   └── remote/        # مصادر البيانات البعيدة
│   ├── models/            # نماذج البيانات
│   └── repositories/      # تنفيذات المستودعات
│
├── domain/                # طبقة المنطق التجاري
│   ├── entities/          # الكيانات الأساسية
│   ├── repositories/      # واجهات المستودعات
│   └── usecases/          # حالات الاستخدام
│
├── presentation/         # طبقة العرض
│   ├── bloc/             # مديرو الحالة (BLoC/Cubit)
│   ├── pages/            # صفحات التطبيق
│   └── widgets/          # الويدجت الخاصة بالعرض
│
└── main.dart             # نقطة الدخول للتطبيق
```

## تنظيم الاستيرادات
يتم تنظيم جميع استيرادات المشروع في ملف `imports.dart` الرئيسي، مقسمة حسب الطبقات والوظائف:

### Core (المكونات الأساسية)

#### الثوابت (Constants)
- `api_constants.dart`: ثوابت واجهات البرمجة
- `app_constants.dart`: ثوابت التطبيق العامة
- `assets_boarding.dart`: أصول شاشات الترحيب
- `assets_colors.dart`: ألوان الأصول
- `assets_fonts.dart`: خطوط التطبيق
- `assets_images.dart`: صور التطبيق
- `assets_login_image.dart`: صور تسجيل الدخول
- `assets_weather_png.dart`: صور الطقس بصيغة PNG
- `assets_weathers_svg.dart`: صور الطقس بصيغة SVG
- `collections.dart`: مجموعات البيانات
- `colors.dart`: ألوان التطبيق
- `constants_url.dart`: عناوين URL الثابتة
- `dimensions.dart`: أبعاد العناصر
- `headings.dart`: عناوين النصوص
- `languages.dart`: إعدادات اللغات
- `login_constants.dart`: ثوابت تسجيل الدخول
- `strings.dart`: النصوص الثابتة
- `text_styles.dart`: أنماط النصوص

#### التهيئة (Initialization)
- `app_initialization_service.dart`: خدمة تهيئة التطبيق
- `initialization_constants.dart`: ثوابت التهيئة

#### السمات (Theme)
- `app_theme.dart`: سمة التطبيق
- `text_field_theme.dart`: سمة حقول النص

#### الأخطاء (Errors)
- `api_exception.dart`: استثناءات واجهة البرمجة
- `location_exception.dart`: استثناءات الموقع

#### الأدوات المساعدة (Utils)
- `cubit_observer.dart`: مراقب Cubit
- `extensions/date_time_extensions.dart`: امتدادات التاريخ والوقت
- `get_weather_icons.dart`: الحصول على أيقونات الطقس
- `logging/logger_service.dart`: خدمة التسجيل
- `logging/logging.dart`: تسجيل الأحداث
- `services/api_keys_service.dart`: خدمة مفاتيح واجهة البرمجة
- `services/secure_storage_service.dart`: خدمة التخزين الآمن

### Data (البيانات)

#### مصادر البيانات المحلية (Local Data Sources)
- `database/database_helper.dart`: مساعد قاعدة البيانات
- `shared_prefs.dart`: التفضيلات المشتركة

#### مصادر البيانات البعيدة (Remote Data Sources)
- `api/api_helper.dart`: مساعد واجهة البرمجة
- `api/api_weather_week.dart`: واجهة برمجة الطقس الأسبوعي
- `api/api_wether.dart`: واجهة برمجة الطقس
- `api/dio_helper.dart`: مساعد Dio
- `api/geolocator.dart`: محدد الموقع الجغرافي
- `api/weather_service.dart`: خدمة الطقس
- `firebase/storage_service.dart`: خدمة تخزين Firebase
- `google_drive/google_drive_service.dart`: خدمة Google Drive

#### المستودعات (Repositories)
- `auth_repository.dart`: مستودع المصادقة
- `storage_repository.dart`: مستودع التخزين
- `user_repository.dart`: مستودع المستخدم

#### نماذج البيانات (Models)

##### المحاصيل (Crops)
- `disease_model.dart`: نموذج المرض
- `disease_referral_Info_model.dart`: نموذج معلومات إحالة المرض
- `disease_type_model.dart`: نموذج نوع المرض
- `plant_category_model.dart`: نموذج فئة النبات
- `plant_operation_category_model.dart`: نموذج فئة عملية النبات
- `plant_operation_model.dart`: نموذج عملية النبات
- `plant_referral_data_model.dart`: نموذج بيانات إحالة النبات
- `plants_model.dart`: نموذج النباتات

##### الصفحة الرئيسية (Home)
- `card_home_model.dart`: نموذج بطاقة الصفحة الرئيسية
- `slider_model.dart`: نموذج شريط التمرير

##### المعالم (Landmarks)
- `agricultural_landmark_model.dart`: نموذج المعلم الزراعي
- `crest_months.dart`: أشهر القمة
- `gorian_months.dart`: الأشهر الغورية
- `hemiari_months.dart`: الأشهر الحميرية
- `landmark_mapper.dart`: محول المعالم

##### أخرى (Others)
- `onboarding/on_borading_model.dart`: نموذج الترحيب
- `prediction.dart`: نموذج التنبؤ
- `user.dart`: نموذج المستخدم
- `weather/hourly_weather_model.dart`: نموذج الطقس بالساعة
- `weather/weekly_model.dart`: نموذج الطقس الأسبوعي

### Domain (المنطق التجاري)

#### الكيانات (Entities)
- `landmark_entity.dart`: كيان المعلم
- `prediction_entity.dart`: كيان التنبؤ
- `user_entity.dart`: كيان المستخدم

#### واجهات المستودعات (Repository Interfaces)
- `auth_repository_interface.dart`: واجهة مستودع المصادقة
- `storage_repository_interface.dart`: واجهة مستودع التخزين
- `user_repository_interface.dart`: واجهة مستودع المستخدم

#### حالات الاستخدام (Use Cases)

##### المصادقة (Auth)
- `create_user_with_email_password.dart`: إنشاء مستخدم بالبريد الإلكتروني وكلمة المرور
- `has_user_completed_profile.dart`: التحقق من اكتمال ملف المستخدم
- `sign_in_with_email_password.dart`: تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- `sign_in_with_google.dart`: تسجيل الدخول بحساب Google
- `sign_in_with_phone.dart`: تسجيل الدخول برقم الهاتف
- `sign_out.dart`: تسجيل الخروج
- `verify_otp.dart`: التحقق من رمز OTP

##### المستخدم (User)
- `get_user_data.dart`: الحصول على بيانات المستخدم
- `update_user_data.dart`: تحديث بيانات المستخدم

### Presentation (العرض)

#### مكتبات إدارة الحالة (Bloc/Cubit)
- `agricultural_crops/crops_cubit.dart`: Cubit المحاصيل الزراعية
- `auth/core/auth_cubit.dart`: Cubit المصادقة الأساسي
- `auth/login/login_cubit.dart`: Cubit تسجيل الدخول
- `auth/phone/phone_auth_cubit.dart`: Cubit مصادقة الهاتف
- `auth/register/register_cubit.dart`: Cubit التسجيل
- `auth/register_profile/register_profile_cubit.dart`: Cubit تسجيل الملف الشخصي
- `community_forum/posts_cubit.dart`: Cubit المنشورات
- `community_forum/posts_state.dart`: حالة المنشورات
- `landmarks/information_cubit.dart`: Cubit معلومات المعالم
- `settinges/settinges.dart`: Cubit الإعدادات
- `splash/splash.dart`: Cubit شاشة البداية
- `update_profile/update_profile.dart`: Cubit تحديث الملف الشخصي
- `weather/wether_cubit.dart`: Cubit الطقس

#### الصفحات (Pages)
- `agricultural_crops/agricultural_crops.dart`: صفحة المحاصيل الزراعية
- `auth/login/login_page.dart`: صفحة تسجيل الدخول
- `auth/login/optimized_login_screen.dart`: شاشة تسجيل الدخول المحسنة
- `auth/phone/otp_verification_page.dart`: صفحة التحقق من رمز OTP
- `auth/phone/phone_input_page.dart`: صفحة إدخال رقم الهاتف
- `auth/register/register_page.dart`: صفحة التسجيل
- `auth/register_profile/register_profile_page.dart`: صفحة تسجيل الملف الشخصي
- `community_forum/comunity_forum.dart`: صفحة منتدى المجتمع
- `education/education.dart`: صفحة التعليم
- `government/government.dart`: صفحة الدعم الحكومي
- `home/home_page.dart`: الصفحة الرئيسية
- `landmarks/information.dart`: صفحة المعلومات
- `marketing_products/marketing_products.dart`: صفحة تسويق المنتجات
- `on_boarding/on_bording.dart`: صفحة الترحيب
- `pests_and_diseases/pests_and_diseases.dart`: صفحة الآفات والأمراض
- `profile/profile.dart`: صفحة الملف الشخصي
- `reach_engineer/reach_engineer.dart`: صفحة التواصل مع المهندس
- `search/search.dart`: صفحة البحث
- `weather/7day_weather_view.dart`: عرض الطقس لمدة 7 أيام
- `weather/weather_view.dart`: عرض الطقس

#### المكونات (Widgets)

##### المحاصيل الزراعية (Agricultural Crops)
- `crop_card.dart`: بطاقة المحصول
- `crop_detail_page.dart`: صفحة تفاصيل المحصول
- `crop_list.dart`: قائمة المحاصيل
- `diseases.dart`: الأمراض
- `inf_description.dart`: وصف المعلومات
- `operations.dart`: العمليات
- `show_diseases.dart`: عرض الأمراض

##### المصادقة (Auth)
- `login/divider_with_text.dart`: فاصل مع نص
- `login/optimized_button.dart`: زر محسن
- `login/optimized_text_field.dart`: حقل نص محسن
- `login/rich_text_link.dart`: رابط نص غني
- `phone/otp_digit_field.dart`: حقل رقم OTP
- `phone/otp_input_form.dart`: نموذج إدخال OTP
- `phone/phone_auth_button.dart`: زر مصادقة الهاتف
- `phone/phone_input_form.dart`: نموذج إدخال الهاتف
- `phone/resend_otp_button.dart`: زر إعادة إرسال OTP
- `register_profile/phone_display.dart`: عرض الهاتف
- `register_profile/profile_form_fields.dart`: حقول نموذج الملف الشخصي
- `register_profile/profile_image_picker.dart`: منتقي صورة الملف الشخصي
- `register_profile/profile_submit_button.dart`: زر إرسال الملف الشخصي

##### منتدى المجتمع (Community Forum)
- `card_post_user.dart`: بطاقة منشور المستخدم
- `comment_and_like.dart`: التعليق والإعجاب
- `new_post_screen.dart`: شاشة منشور جديد
- `post_media.dart`: وسائط المنشور
- `top_add_post.dart`: إضافة منشور في الأعلى

##### الصفحة الرئيسية (Home)
- `slider_item.dart`: عنصر شريط التمرير
- `text_service.dart`: خدمة النص

##### المعالم (Landmarks)
- `calendar_display.dart`: عرض التقويم
- `information_section.dart`: قسم المعلومات
- `landmark_card.dart`: بطاقة المعلم
- `landmark_detail_page.dart`: صفحة تفاصيل المعلم
- `landmarks_content.dart`: محتوى المعالم

##### الترحيب (On Boarding)
- `on_boarding_widget.dart`: ويدجت الترحيب

##### المكونات المشتركة (Shared)
- `appbar.dart`: شريط التطبيق
- `cachd_net_image.dart`: صورة الشبكة المخزنة مؤقتًا
- `circular_progress.dart`: تقدم دائري
- `conditional_single.dart`: شرط فردي
- `custom_loading_animation.dart`: رسوم متحركة للتحميل المخصص
- `custom_social_button.dart`: زر اجتماعي مخصص
- `divider_login.dart`: فاصل تسجيل الدخول
- `driver.dart`: سائق
- `driver2.dart`: سائق 2
- `drop_down.dart`: قائمة منسدلة
- `enhanced_video_player.dart`: مشغل فيديو محسن
- `full_screen_image_viewer.dart`: عارض صور ملء الشاشة
- `info_card.dart`: بطاقة معلومات
- `lazy_loading_grid.dart`: شبكة تحميل كسول
- `multiple_images_grid.dart`: شبكة صور متعددة
- `primary_button_inmy.dart`: زر أساسي
- `search.dart`: بحث
- `tab_item.dart`: عنصر علامة تبويب
- `text_form_filed.dart`: حقل نموذج نص
- `text_register_and_button.dart`: تسجيل نص وزر

##### الطقس (Weather)
- `hourly_forecast_view.dart`: عرض التنبؤ بالساعة
- `preferred_size_appbar.dart`: شريط تطبيق بحجم مفضل
- `row_weather.dart`: صف الطقس
- `stack_home_weather.dart`: مكدس الطقس الرئيسي
- `tody_card.dart`: بطاقة اليوم

### Routing (التنقل)
- `app_router.dart`: موجه التطبيق
- `initial_route_helper.dart`: مساعد المسار الأولي
- `model/route_model.dart`: نموذج المسار
- `route_constants.dart`: ثوابت المسار

## إرشادات التطوير

### استخدام اللغة العربية
- يجب استخدام اللغة العربية في جميع التعليقات والتوثيق
- الحفاظ على اتساق اللغة في جميع أنحاء المشروع

### تحديث ملف الاستيرادات
- عند إضافة ملفات جديدة، يجب إضافتها إلى المكان المناسب في ملف `imports.dart`
- الحفاظ على التنظيم المنطقي للاستيرادات حسب المجموعات المحددة

### استخدام ملفات الفهرسة
- إنشاء ملفات `index.dart` في المجلدات الفرعية لتسهيل الاستيراد
- هذا يقلل من تعقيد الاستيرادات ويجعل الكود أكثر قابلية للصيانة

### التحقق من الملفات المشتركة
- التأكد دائمًا من عدم وجود ثوابت أو مكونات مشابهة قبل إنشاء جديدة
- استخدام الثوابت والمكونات الموجودة لتجنب التكرار

### الالتزام بالهندسة المعمارية النظيفة
- اتباع نمط Clean Architecture مع فصل واضح بين الطبقات
- استخدام Cubit بدلاً من StatefulWidget لإدارة حالة التطبيق

## الميزات الرئيسية

### المصادقة
- تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
- تسجيل الدخول برقم الهاتف
- تسجيل الدخول بحساب Google
- إنشاء حساب جديد
- تحديث الملف الشخصي

### الطقس
- عرض حالة الطقس الحالية
- توقعات الطقس على مدار الساعة
- توقعات الطقس الأسبوعية

### المحاصيل الزراعية
- قائمة المحاصيل
- تفاصيل المحصول
- التقويم الزراعي
- أمراض المحاصيل

### المعالم الزراعية
- قائمة المعالم
- تفاصيل المعلم
- خريطة المعالم
- عرض البيانات

### منتدى المجتمع
- عرض المنشورات
- إضافة منشور جديد
- التعليق والإعجاب

## التقنيات المستخدمة

### إدارة الحالة
- Flutter Bloc/Cubit

### قواعد البيانات
- Firebase
- التخزين المحلي (Shared Preferences)

### الخدمات الخارجية
- خدمة الطقس
- Google Drive
- خدمات الموقع الجغرافي