name: agriculture
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  ### إدارة الحالة ###
  flutter_bloc: ^9.1.1                  # لإدارة الحالة بنمط BLoC (يفضل استخدامه أو Riverpod وليس الاثنين معاً)
  flutter_riverpod: ^2.6.1               # بديل حديث لإدارة الحالة (اختر إما BLoC أو Riverpod)
  riverpod: ^2.6.1                       # الإصدار الأساسي من Riverpod

  ### الواجهة والتصميم ###
  flutter_conditional_rendering: ^2.1.0  # عرض عناصر واجهة مستخدم شرطية
  flutter_native_splash: ^2.4.4          # إنشاء شاشة بدء (Splash Screen) مخصصة
  flutter_svg: ^2.1.0                    # عرض ملفات SVG
  shimmer: ^3.0.0                        # تأثير توهج أثناء التحميل
  smooth_page_indicator: ^1.2.1          # مؤشر صفحات أنيق لـ PageView
  loading_animation_widget: ^1.3.0       # رسوميات تحميل متحركة
  list_wheel_scroll_view_nls: ^0.0.3     # عرض قوائم دائرية (مثل منتقي التواريخ)
  photo_view: ^0.15.0                    # معاينة الصور مع تكبير/تصغير

  ### الموقع والخرائط ###
  geolocator: ^13.0.0                    # الحصول على موقع GPS
  geolocator_android: ^4.2.0             # تنفيذ خاص بأندرويد لـ geolocator

  ### المصادقة وخدمات جوجل ###
  google_sign_in: ^6.3.0                 # تسجيل الدخول بحساب جوجل
  googleapis: ^14.0.0                    # وصول لخدمات جوجل (Drive, Gmail, إلخ)
  googleapis_auth: ^1.3.0                # مصادقة خدمات جوجل

  ### الشبكات والاتصالات ###
  http: ^0.13.6                        # إرسال طلبات HTTP (يفضل الترقية لـ 0.13.6)
  retry: ^3.1.2                          # إعادة محاولة الطلبات الفاشلة تلقائياً
  url_launcher: ^6.2.1                   # فتح روابط خارجية في المتصفح

  ### قواعد البيانات والتخزين ###
  shared_preferences: ^2.5.3             # تخزين بيانات بسيطة (إعدادات، tokens)
  sqflite: ^2.4.1                        # قاعدة بيانات SQLite محلية

  ### الوسائط المتعددة ###
  video_player: ^2.9.5                   # تشغيل الفيديو الأساسي
  chewie: ^1.11.3                        # واجهة تحكم متقدمة للفيديو
  image_picker: ^1.1.2                   # اختيار الصور من المعرض/الكاميرا
  flutter_image_compress: ^2.4.0         # ضغط الصور قبل رفعها

  ### أدوات مساعدة ###
  uuid: ^4.4.0                          # إنشاء معرفات فريدة (للملفات، المستخدمين، إلخ)
  intl: ^0.19.0                          # تنسيق التواريخ والأرقام بلغات متعددة
  jiffy: ^6.3.2                          # معالجة التواريخ بسهولة
  path: ^1.9.0                           # التعامل مع مسارات الملفات
  path_provider: ^2.1.5                  # الحصول على مسارات التخزين على الجهاز
  cross_file: ^0.3.4+2                   # نقل الملفات بين المنصات
  permission_handler: ^11.3.0            # طلب صلاحيات النظام (كاميرا، موقع، إلخ)
  share_plus: ^11.0.0                    # مشاركة المحتوى مع تطبيقات أخرى
  weather_icons: ^3.0.0                  # أيقونات جاهزة للطقس

  ### الذكاء الاصطناعي ###
  tflite_flutter: ^0.11.0                # تشغيل نماذج TensorFlow Lite
#  dialogflow_grpc: ^4.0.0               # دمج Dialogflow للدردشة الذكية


  dialogflow_flutter: ^3.0.0  # بديل حديث

  dialogflow_grpc:
    path: ./local_packages/dialogflow_grpc






#####################################################
  ### الاختبارات ###

  bloc_test: ^10.0.0                     # اختبارات لـ BLoC
  mockito: ^5.4.4                        # إنشاء كائنات وهمية للاختبار

  ### أدوات التطوير ###
  build_runner: ^2.4.8                   # توليد الأكواد تلقائياً
  flutter_lints: ^5.0.0                  # تحسين جودة الكود
  flutter_localization: ^0.3.2           # دعم الترجمة (ينقل لـ dependencies إذا استخدمته)



dev_dependencies:
  flutter_test:
    sdk: flutter




  flutter_localizations:
    sdk: flutter



flutter_native_splash:
  image: assets/images/splash/play1.gif
  color: "#00bfa5"

flutter:

  uses-material-design: true
  assets:
    - assets/
    - assets/fonts/
    - assets/fonts/cairo/
    - assets/fonts/sultan/
    - assets/fonts/eimessiri/
    - assets/images/
    - assets/images/slider/
    - assets/images/splash/
    - assets/images/phone_otp/
    - assets/images/login_image/
    - assets/images/wether_icon/
    - assets/images/register_image/
    - assets/iconLogin/
    - assets/images/on_boarding/
    - assets/images/on_boarding/image1.png
    - assets/db/agricluture_DB.db
    - assets/wether_svg/
    - assets/wether_svg/static/
    - assets/wether_svg/animated/
    - assets/icons/
    - assets/config/
    - assets/credentials/

  fonts:
    - family: sultan
      fonts:
        - asset: assets/fonts/sultan/sultan.ttf
    #       - asset: fonts/Schyler-Italic.ttf
    #         style: italic
    - family: cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
          weight: 700

    - family: cairo1
      fonts:
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
    - family: ElMessiri
      fonts:
        - asset: assets/fonts/eimessiri/ElMessiri-Bold-3.ttf