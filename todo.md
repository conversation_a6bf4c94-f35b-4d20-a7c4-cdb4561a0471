# قائمة المهام لمشروع المرشد الزراعي (محدث)

- [x] إنشاء هيكل المجلدات الأساسي للمشروع.
- [x] إنشاء ملف `lib/main.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/advisor_dashboard_screen.dart` (الإصدار الأولي).
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/tabs/consultations_tab.dart` (الإصدار الأولي).
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/components/consultation_card.dart` (الإصدار الأولي).
- [x] إنشاء ملف `lib/data/models/consultation.dart`.
- [x] إنشاء ملف `lib/core/constants/colors.dart` (مؤقت).
- [x] إنشاء ملف `lib/core/utils/date_utils.dart` (مؤقت).
- [x] إنشاء ملف `lib/core/widgets/stat_card.dart`.
- [x] تحديث `lib/presentation/advisor_dashboard/advisor_dashboard_screen.dart` ليشمل معلومات المرشد والإحصائيات السريعة وقائمة الإشعارات.
- [x] إنشاء ملف `lib/data/models/appointment.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/components/appointment_card.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/tabs/appointments_tab.dart`.
- [x] إنشاء ملف `lib/data/models/plant_monitoring.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/components/plant_card.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/tabs/plant_monitoring_tab.dart`.
- [x] إنشاء ملف `lib/presentation/advisor_dashboard/tabs/reports_tab.dart`.
- [x] إنشاء ملفات هيكلية إضافية (مثل `strings.dart`, `assets.dart`, `repositories`, `bloc`, `services`) كمؤشرات أو فارغة.
- [x] التحقق من تكامل جميع المكونات والملفات.
- [x] ضغط المشروع المحدث وإرساله للمستخدم.

## المهام الجديدة:

- [ ] **طبقة البيانات (Data Layer):**
  - [ ] إنشاء كود `lib/data/datasources/firebase_datasource.dart` (للتفاعل مع Firestore).
  - [ ] إنشاء كود `lib/data/repositories/advisor_repository.dart` (لاستخدام Datasource).
  - [ ] (اختياري) إنشاء كود أساسي لـ `lib/data/datasources/local_storage.dart` و `lib/data/repositories/local_data.dart` إذا لزم الأمر.
- [ ] **إدارة الحالة (BLoC/Cubit):**
  - [ ] إنشاء كود `lib/bloc/advisor_state.dart` (تعريف الحالات: Initial, Loading, Loaded, Error).
  - [ ] إنشاء كود `lib/bloc/advisor_cubit.dart` (منطق جلب البيانات وإصدار الحالات).
- [ ] **تحديث الواجهات (Presentation Layer):**
  - [ ] ربط `lib/main.dart` مع `BlocProvider` لتوفير الـ Cubit.
  - [ ] تحديث `lib/presentation/advisor_dashboard/tabs/consultations_tab.dart` لاستخدام Cubit.
  - [ ] تحديث `lib/presentation/advisor_dashboard/tabs/appointments_tab.dart` لاستخدام Cubit.
  - [ ] تحديث `lib/presentation/advisor_dashboard/tabs/plant_monitoring_tab.dart` لاستخدام Cubit.
  - [ ] (اختياري) تحديث `lib/presentation/advisor_dashboard/advisor_dashboard_screen.dart` لعرض بيانات ديناميكية من Cubit (مثل الإحصائيات).
- [ ] **الخدمات (Services):**
  - [ ] إنشاء كود أساسي أو مؤقت لـ `lib/services/notification_service.dart`.
  - [ ] إنشاء كود أساسي أو مؤقت لـ `lib/services/ai_assistant.dart`.
- [ ] **ملفات أخرى:**
  - [ ] إنشاء كود أساسي أو مؤقت لـ `lib/core/utils/formatters.dart`.
  - [ ] إنشاء كود أساسي أو مؤقت لـ `lib/core/widgets/status_chip.dart`.
  - [ ] إنشاء كود أساسي أو مؤقت لـ `lib/core/widgets/custom_app_bar.dart`.
- [ ] **التكامل والمراجعة:**
  - [x] إضافة تعليقات توضيحية باللغة العربية في جميع الملفات الجديدة والمحدثة.
  - [x] مراجعة شاملة للكود للتأكد من خلوه من الأخطاء والتكامل الصحيح.
  - [x] تجهيز شرح لخطوات تهيئة Firebase.
  - [x] تجهيز شرح وملاحظات حول تكامل Google Drive (بناءً على توضيح المستخدم أو كإرشادات عامة).
- [ ] **التسليم النهائي:**
  - [x] ضغط المشروع النهائي وإرساله للمستخدم مع الشرح المفصل.
