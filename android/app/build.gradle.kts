plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

android {
    namespace = "com.example.agriculture"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.agriculture"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = 33
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}
dependencies {
    // Import the BoM for the Firebase platform
    implementation(platform("com.google.firebase:firebase-bom:33.6.0"))

    // Add the dependency for the Firebase Authentication library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation("com.google.firebase:firebase-auth-ktx")
    implementation("com.google.firebase:firebase-firestore-ktx")
//    implementation("com.google.firebase:firebase-core")
}



//dependencies {
//    // Import the BoM for the Firebase platform
//    // Use the latest version of the Firebase BoM
//    implementation(platform("com.google.firebase:firebase-bom:33.13.0")) // <--- هذا السطر الجديد
//
//    // Declare the dependencies for the desired Firebase products
//    // When using the BoM, you don't specify versions in Firebase dependencies
//    implementation("com.google.firebase:firebase-auth-ktx") // بدون تحديد إصدار هنا (مُفضل لـ Kotlin)
//    // أو إذا كنت تستخدم Java فقط: implementation("com.google.firebase:firebase-auth") // بدون تحديد إصدار هنا
//
//    // تأكد أيضاً من وجود هذا السطر إذا لم يكن موجوداً بالفعل
//    implementation("com.google.firebase:firebase-core")
//
//    // أضف أي مكتبات Firebase أخرى تستخدمها بنفس الطريقة (بدون تحديد إصدار)
//    // implementation("com.google.firebase:firebase-firestore-ktx") // بدون تحديد إصدار هنا
//
//    // أضف مكتبات أخرى غير Firebase بشكل طبيعي مع تحديد إصداراتها
//    // implementation("androidx.appcompat:appcompat:1.6.1")
//    // implementation("com.google.android.material:material:1.10.0")
//    // ... إلخ
//}
