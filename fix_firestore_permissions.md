# 🔧 إصلاح مشكلة صلاحيات Firestore

## 🚨 المشكلة الحالية

```
[cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
```

المستخدم مسجل الدخول بنجاح لكن لا يستطيع الوصول إلى بيانات Firestore.

## ✅ الحل: تحديث قواعد Firestore

### 1. الذهاب إلى Firebase Console

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك: `agriculture-65d2e`
3. من القائمة الجانبية، اختر **Firestore Database**
4. اختر تبويب **Rules**

### 2. استبدال القواعد الحالية

انسخ والصق القواعد التالية في محرر القواعد:

```javascript
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد مجموعة المستخدمين
    match /users/{userId} {
      // السماح للمستخدم بقراءة وكتابة بياناته الخاصة فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // السماح بإنشاء مستند جديد للمستخدم المسجل الدخول
      allow create: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد مجموعة المنتدى المجتمعي
    match /community_posts/{postId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمؤلف فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.authorId == request.auth.uid);
      
      // السماح بإنشاء منشور جديد للمستخدمين المسجلين
      allow create: if request.auth != null && 
                    request.resource.data.authorId == request.auth.uid;
    }
    
    // قواعد مجموعة الاستشارات الزراعية
    match /agricultural_consultations/{consultationId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمؤلف فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.userId == request.auth.uid);
      
      // السماح بإنشاء استشارة جديدة للمستخدمين المسجلين
      allow create: if request.auth != null && 
                    request.resource.data.userId == request.auth.uid;
    }
    
    // قواعد للبيانات العامة (قراءة فقط)
    match /agricultural_crops/{cropId} {
      allow read: if true;
      allow write: if false;
    }
    
    match /agricultural_landmarks/{landmarkId} {
      allow read: if true;
      allow write: if false;
    }
    
    match /pests_and_diseases/{pestId} {
      allow read: if true;
      allow write: if false;
    }
    
    match /agricultural_news/{newsId} {
      allow read: if true;
      allow write: if false;
    }
    
    match /weather_data/{weatherId} {
      allow read: if true;
      allow write: if false;
    }
    
    // قواعد افتراضية: رفض الوصول لأي مجموعة أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

### 3. نشر القواعد

1. اضغط على زر **Publish** لنشر القواعد الجديدة
2. انتظر حتى يتم النشر بنجاح

## 🚀 حل سريع مؤقت (للتطوير فقط)

إذا كنت تريد حلاً سريعاً للتطوير، يمكنك استخدام هذه القواعد المؤقتة:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

⚠️ **تحذير**: هذه القواعد تسمح لأي مستخدم مسجل الدخول بالوصول إلى جميع البيانات. استخدمها للتطوير فقط!

## 🧪 اختبار الإصلاح

بعد تحديث القواعد:

1. أعد تشغيل التطبيق
2. جرب تسجيل الدخول
3. تحقق من عدم ظهور خطأ `permission-denied`

## 📋 ملاحظات مهمة

1. **الأمان**: القواعد الجديدة تضمن أن كل مستخدم يمكنه الوصول إلى بياناته فقط
2. **البيانات العامة**: المحاصيل والمعالم والأخبار متاحة للقراءة للجميع
3. **المنتدى**: المستخدمون يمكنهم قراءة جميع المنشورات وكتابة منشوراتهم فقط

## 🔄 إذا استمرت المشكلة

1. تأكد من نشر القواعد بنجاح
2. انتظر دقيقة أو دقيقتين لتطبيق التغييرات
3. أعد تشغيل التطبيق
4. تحقق من أن المستخدم مسجل الدخول بنجاح
