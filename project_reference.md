# ملف مرجعي لمشروع تطبيق الزراعة

##Agriculture هيكل المشروع

المشروع يتبع هندسة معمارية نظيفة (Clean Architecture) مع تقسيم واضح للطبقات:

```
lib/
├── core/                  # المكونات الأساسية المشتركة
│   ├── constants/         # الثوابت (ألوان، نصوص...)
│   ├── errors/            # معالجة الأخطاء
│   ├── initialization/    # خدمات التهيئة
│   ├── network/           # إعدادات الشبكة العامة
│   ├── theme/             # سمات التطبيق
│   └── utils/             # أدوات مساعدة
│
├── data/                  # طبقة البيانات
│   ├── datasources/       # مصادر البيانات
│   │   ├── local/         # مصادر البيانات المحلية
│   │   └── remote/        # مصادر البيانات البعيدة
│   ├── models/            # نماذج البيانات
│   └── repositories/      # تنفيذات المستودعات
│
├── domain/                # طبقة المنطق التجاري
│   ├── entities/          # الكيانات الأساسية
│   ├── repositories/      # واجهات المستودعات
│   └── usecases/          # حالات الاستخدام
│
├── presentation/         # طبقة العرض
│   ├── bloc/             # مديرو الحالة (BLoC/Cubit)
│   ├── pages/            # صفحات التطبيق
│   └── widgets/          # الويدجت الخاصة بالعرض
│
├── routing/              # التنقل بين الصفحات
│   ├── model/            # نماذج التنقل
│   └── ...               # ملفات التنقل الأخرى
│
├── app.dart              # إعداد التطبيق
├── imports.dart          # استيرادات المشروع
└── main.dart             # نقطة الدخول للتطبيق
```

## ملفات التكوين

### assets/config/app_config.json
ملف تكوين التطبيق الذي يحتوي على إعدادات مثل:
- معلومات التطبيق (الاسم، الإصدار)
- عناوين URL للواجهات البرمجية
- إعدادات الميزات (الطقس، المنتدى المجتمعي، الملف الشخصي)
- إعدادات السمات (الألوان، الخطوط)

### assets/credentials/service_account.json
ملف اعتماد حساب الخدمة لـ Google API، يستخدم للوصول إلى خدمات Google مثل Google Drive.

## الميزات الرئيسية

1. **المصادقة**
   - تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
   - تسجيل الدخول برقم الهاتف
   - تسجيل الدخول بحساب Google
   - إنشاء حساب جديد
   - تحديث الملف الشخصي

2. **الطقس**
   - عرض حالة الطقس الحالية
   - توقعات الطقس على مدار الساعة
   - توقعات الطقس الأسبوعية

3. **المحاصيل الزراعية**
   - قائمة المحاصيل
   - تفاصيل المحصول
   - التقويم الزراعي
   - أمراض المحاصيل

4. **المعالم الزراعية**
   - قائمة المعالم
   - تفاصيل المعلم
   - خريطة المعالم
   - عرض البيانات

5. **منتدى المجتمع**
   - عرض المنشورات
   - إضافة منشور جديد
   - التعليق والإعجاب

6. **التعليم والتدريب**

7. **الدعم الحكومي**

8. **تسويق المنتجات**

9. **الآفات والأمراض**

10. **التواصل مع المهندس**

## إدارة الحالة

المشروع يستخدم Cubit لإدارة الحالة، مع تنظيم واضح للكيوبت حسب الميزات:

- `auth_cubit.dart`: إدارة حالة المصادقة
- `login_cubit.dart`: إدارة حالة تسجيل الدخول
- `register_cubit.dart`: إدارة حالة التسجيل
- `crops_cubit.dart`: إدارة حالة المحاصيل الزراعية
- `information_cubit.dart`: إدارة حالة المعالم الزراعية
- `posts_cubit.dart`: إدارة حالة المنشورات
- `wether_cubit.dart`: إدارة حالة الطقس

## الثوابت

المشروع يستخدم ملفات ثوابت منظمة لتجنب القيم الثابتة في الكود:

- `app_constants.dart`: ثوابت التطبيق العامة
- `api_constants.dart`: ثوابت واجهات البرمجة
- `assets_colors.dart`: ألوان التطبيق
- `assets_fonts.dart`: خطوط التطبيق
- `headings.dart`: عناوين النصوص
- `strings.dart`: النصوص الثابتة
- `route_constants.dart`: ثوابت المسارات

## التنقل

المشروع يستخدم نظام تنقل مركزي:

- `app_router.dart`: موجه التطبيق
- `initial_route_helper.dart`: مساعد المسار الأولي
- `navigation_service.dart`: خدمة التنقل
- `route_constants.dart`: ثوابت المسارات

## مصادر البيانات

### مصادر البيانات المحلية
- `database_helper.dart`: مساعد قاعدة البيانات
- `shared_prefs.dart`: التفضيلات المشتركة

### مصادر البيانات البعيدة
- `api_helper.dart`: مساعد واجهة البرمجة
- `weather_service.dart`: خدمة الطقس
- `storage_service.dart`: خدمة تخزين Firebase
- `google_drive_service.dart`: خدمة Google Drive

## المستودعات

- `auth_repository.dart`: مستودع المصادقة
- `user_repository.dart`: مستودع المستخدم
- `storage_repository.dart`: مستودع التخزين

## إرشادات التطوير

1. **استخدام اللغة العربية**
   - استخدام اللغة العربية في التعليقات والتوثيق
   - الحفاظ على اتساق اللغة في جميع أنحاء المشروع

2. **تحديث ملف الاستيرادات**
   - عند إضافة ملفات جديدة، يجب إضافتها إلى المكان المناسب في ملف `imports.dart`
   - الحفاظ على التنظيم المنطقي للاستيرادات حسب المجموعات المحددة

3. **استخدام ملفات الفهرسة**
   - إنشاء ملفات `index.dart` في المجلدات الفرعية لتسهيل الاستيراد
   - هذا يقلل من تعقيد الاستيرادات ويجعل الكود أكثر قابلية للصيانة

4. **التحقق من الملفات المشتركة**
   - التأكد دائمًا من عدم وجود ثوابت أو مكونات مشابهة قبل إنشاء جديدة
   - استخدام الثوابت والمكونات الموجودة لتجنب التكرار

5. **الالتزام بالهندسة المعمارية النظيفة**
   - اتباع نمط Clean Architecture مع فصل واضح بين الطبقات
   - استخدام Cubit بدلاً من StatefulWidget لإدارة حالة التطبيق
   - تجزئة الوظائف لتسهيل الصيانة والتطوير

6. **التركيز على الثوابت والملفات التشاركية**
   - استخدام الثوابت بشكل موحد في جميع أنحاء المشروع
   - تنظيم الثوابت حسب الوظيفة والاستخدام
   - مراجعة الملفات التشاركية قبل إضافة أي مكونات جديدة

7. **إدارة التحديثات والتغييرات**
   - عند إنشاء ملفات جديدة، يجب استبدال الملفات القديمة أو حذفها
   - توثيق سبب التغيير والتحديث في ملف التوثيق
   - الحفاظ على تناسق الكود بعد التحديثات
