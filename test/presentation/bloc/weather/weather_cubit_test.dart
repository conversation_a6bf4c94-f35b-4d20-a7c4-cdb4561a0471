// import 'package:agriculture/presentation/bloc/weather/wether_cubit.dart';
// import 'package:flutter_test/flutter_test.dart';
//
// void main() {
//   late WeatherCubit weatherCubit;
//
//   setUp(() {
//     weatherCubit = WeatherCubit.test();
//   });
//
//   tearDown(() {
//     weatherCubit.close();
//   });
//
//   group('WeatherCubit', () {
//     test('يجب أن تكون الحالة الأولية هي WeatherInitial', () {
//       expect(weatherCubit.state, isA<WeatherInitial>());
//     });
//   });
// }
