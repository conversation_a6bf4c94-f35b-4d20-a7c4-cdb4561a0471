// // Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// // in agriculture/test/presentation/bloc/weather/weather_cubit_test.dart.
// // Do not manually edit this file.
//
// // ignore_for_file: no_leading_underscores_for_library_prefixes
// import 'dart:async' as _i6;
//
// import 'package:agriculture/data/datasources/remote/api/weather_service.dart'
//     as _i5;
// import 'package:agriculture/data/models/weather/hourly_weather_model.dart'
//     as _i3;
// import 'package:agriculture/data/models/weather/weather_model.dart' as _i2;
// import 'package:agriculture/data/models/weather/weekly_model.dart' as _i4;
// import 'package:mockito/mockito.dart' as _i1;
//
// // ignore_for_file: type=lint
// // ignore_for_file: avoid_redundant_argument_values
// // ignore_for_file: avoid_setters_without_getters
// // ignore_for_file: comment_references
// // ignore_for_file: deprecated_member_use
// // ignore_for_file: deprecated_member_use_from_same_package
// // ignore_for_file: implementation_imports
// // ignore_for_file: invalid_use_of_visible_for_testing_member
// // ignore_for_file: must_be_immutable
// // ignore_for_file: prefer_const_constructors
// // ignore_for_file: unnecessary_parenthesis
// // ignore_for_file: camel_case_types
// // ignore_for_file: subtype_of_sealed_class
//
// class _FakeWeatherModel_0 extends _i1.SmartFake implements _i2.WeatherModel {
//   _FakeWeatherModel_0(Object parent, Invocation parentInvocation)
//     : super(parent, parentInvocation);
// }
//
// class _FakeHourlyModel_1 extends _i1.SmartFake implements _i3.HourlyModel {
//   _FakeHourlyModel_1(Object parent, Invocation parentInvocation)
//     : super(parent, parentInvocation);
// }
//
// class _FakeWeeklyModel_2 extends _i1.SmartFake implements _i4.WeeklyModel {
//   _FakeWeeklyModel_2(Object parent, Invocation parentInvocation)
//     : super(parent, parentInvocation);
// }
//
// /// A class which mocks [WeatherService].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockWeatherService extends _i1.Mock implements _i5.WeatherService {
//   MockWeatherService() {
//     _i1.throwOnMissingStub(this);
//   }
//
//   @override
//   _i6.Future<_i2.WeatherModel> getCurrentWeather() =>
//       (super.noSuchMethod(
//             Invocation.method(#getCurrentWeather, []),
//             returnValue: _i6.Future<_i2.WeatherModel>.value(
//               _FakeWeatherModel_0(
//                 this,
//                 Invocation.method(#getCurrentWeather, []),
//               ),
//             ),
//           )
//           as _i6.Future<_i2.WeatherModel>);
//
//   @override
//   _i6.Future<_i2.WeatherModel> getWeatherByCity(String? city) =>
//       (super.noSuchMethod(
//             Invocation.method(#getWeatherByCity, [city]),
//             returnValue: _i6.Future<_i2.WeatherModel>.value(
//               _FakeWeatherModel_0(
//                 this,
//                 Invocation.method(#getWeatherByCity, [city]),
//               ),
//             ),
//           )
//           as _i6.Future<_i2.WeatherModel>);
//
//   @override
//   _i6.Future<_i3.HourlyModel> getHourlyForecast() =>
//       (super.noSuchMethod(
//             Invocation.method(#getHourlyForecast, []),
//             returnValue: _i6.Future<_i3.HourlyModel>.value(
//               _FakeHourlyModel_1(
//                 this,
//                 Invocation.method(#getHourlyForecast, []),
//               ),
//             ),
//           )
//           as _i6.Future<_i3.HourlyModel>);
//
//   @override
//   _i6.Future<_i4.WeeklyModel> getWeeklyForecast() =>
//       (super.noSuchMethod(
//             Invocation.method(#getWeeklyForecast, []),
//             returnValue: _i6.Future<_i4.WeeklyModel>.value(
//               _FakeWeeklyModel_2(
//                 this,
//                 Invocation.method(#getWeeklyForecast, []),
//               ),
//             ),
//           )
//           as _i6.Future<_i4.WeeklyModel>);
//
//   @override
//   _i6.Future<void> clearWeatherCache() =>
//       (super.noSuchMethod(
//             Invocation.method(#clearWeatherCache, []),
//             returnValue: _i6.Future<void>.value(),
//             returnValueForMissingStub: _i6.Future<void>.value(),
//           )
//           as _i6.Future<void>);
// }
