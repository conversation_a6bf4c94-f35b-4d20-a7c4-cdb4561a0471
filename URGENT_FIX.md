# 🚨 إصلاح عاجل: مشكلة صلاحيات Firestore

## المشكلة
```
[cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
```

## الحل السريع (5 دقائق)

### 1. اذهب إلى Firebase Console
- [Firebase Console](https://console.firebase.google.com/)
- اختر مشروع: `agriculture-65d2e`
- Firestore Database → Rules

### 2. استبدل القواعد بهذا الكود:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3. اضغط Publish

### 4. أعد تشغيل التطبيق

## ✅ النتيجة المتوقعة
- ✅ اختفاء خطأ permission-denied
- ✅ عمل التطبيق بشكل طبيعي
- ✅ إمكانية الوصول لبيانات المستخدم

---

**ملاحظة**: هذا حل مؤقت للتطوير. للإنتاج، استخدم القواعد المفصلة في `firestore.rules`
