# دليل تكامل Google Drive مع Flutter (لرفع الملفات)

يهدف هذا الدليل إلى تقديم نظرة عامة وخطوات أساسية لتمكين تطبيق Flutter الخاص بك (مثل "المرشد الزراعي") من رفع الملفات (كالصور المرفقة بالاستشارات) إلى Google Drive الخاص بالمستخدم.

**تحذير:** تكامل Google Drive يتضمن خطوات معقدة تتعلق بالمصادقة (OAuth 2.0) وإدارة الأذونات، ويتطلب تعاملًا حذرًا مع بيانات المستخدم ومفاتيح API.

**المتطلبات الأساسية:**

1.  **مشروع Google Cloud Platform (GCP):**
    *   اذهب إلى [Google Cloud Console](https://console.cloud.google.com/).
    *   قم بإنشاء مشروع جديد أو استخدم مشروعًا موجودًا (يمكن أن يكون نفس مشروع Firebase).
2.  **تمكين Google Drive API:**
    *   في مشروع GCP، اذهب إلى "APIs & Services" -> "Library".
    *   ابحث عن "Google Drive API" وقم بتمكينه (Enable).
3.  **إعداد شاشة موافقة OAuth (OAuth consent screen):**
    *   اذهب إلى "APIs & Services" -> "OAuth consent screen".
    *   اختر نوع المستخدم (User Type): "External" (خارجي) إذا كان التطبيق متاحًا لأي مستخدم Google، أو "Internal" إذا كان مقتصرًا على مستخدمي مؤسستك في Google Workspace.
    *   املأ المعلومات المطلوبة (اسم التطبيق، شعار، معلومات الدعم، النطاقات المصرح بها).
    *   **إضافة النطاقات (Scopes):** هذه هي الأذونات التي سيطلبها تطبيقك من المستخدم. لرفع الملفات، ستحتاج على الأقل إلى:
        *   `.../auth/drive.file`: للسماح للتطبيق بإنشاء ملفات جديدة والوصول إلى الملفات التي أنشأها فقط.
        *   أو `.../auth/drive`: للوصول الكامل إلى Google Drive الخاص بالمستخدم (استخدم هذا النطاق بحذر شديد).
        *   أضف النطاقات المطلوبة واحفظ.
    *   قد تحتاج إلى إرسال شاشة الموافقة للمراجعة بواسطة Google إذا اخترت "External" وطلبت نطاقات حساسة.
4.  **إنشاء بيانات اعتماد OAuth 2.0 (OAuth 2.0 Credentials):**
    *   اذهب إلى "APIs & Services" -> "Credentials".
    *   انقر على "Create Credentials" -> "OAuth client ID".
    *   اختر نوع التطبيق (Application type): "Android" أو "iOS" (أو كلاهما).
    *   **Android:** أدخل اسم الحزمة (Package name) الخاص بتطبيقك وتوقيع شهادة SHA-1 (لإصدار الإنتاج).
    *   **iOS:** أدخل معرف الحزمة (Bundle ID) الخاص بتطبيقك.
    *   قم بإنشاء بيانات الاعتماد. ستحصل على **معرف العميل (Client ID)**. قد تحتاج أيضًا إلى إعداد URI لإعادة التوجيه (Redirect URI) لبعض طرق المصادقة.

**التبعيات المطلوبة في `pubspec.yaml`:**

```yaml
dependencies:
  flutter:
    sdk: flutter
  # ... (التبعيات الأخرى)

  # حزم Google APIs
  googleapis: ^11.4.0 # أو أحدث إصدار
  googleapis_auth: ^1.4.1 # أو أحدث إصدار
  http: ^1.1.0 # أو أحدث إصدار

  # حزمة لاختيار الملفات أو الصور
  file_picker: ^6.1.1 # أو image_picker

  # (اختياري) للمساعدة في عملية المصادقة
  url_launcher: ^6.2.1
  # أو يمكنك استخدام حزم متخصصة مثل google_sign_in (لكنها قد لا توفر مباشرةً client للمصادقة)
```

**خطوات التنفيذ البرمجي (نظرة عامة):**

1.  **الحصول على بيانات اعتماد المستخدم (OAuth 2.0 Flow):**
    *   هذه هي الخطوة الأكثر تعقيدًا. تحتاج إلى توجيه المستخدم للموافقة على منح تطبيقك الأذونات (النطاقات) التي حددتها.
    *   يمكن استخدام حزمة `googleapis_auth` للمساعدة في هذا. إحدى الطرق الشائعة هي `obtainAccessCredentialsViaUserConsent`:
        *   ستحتاج إلى `ClientId` الذي أنشأته في GCP.
        *   ستحتاج إلى قائمة النطاقات (`scopes`) المطلوبة.
        *   ستحتاج إلى `http.Client`.
        *   هذه الدالة ستفتح عادةً متصفحًا للمستخدم لتسجيل الدخول والموافقة.
        *   ستحتاج إلى طريقة لالتقاط الاستجابة (عادةً عبر Redirect URI أو خادم محلي مؤقت).
    *   **مثال مبسط جداً (قد يحتاج لتعديل كبير):**
        ```dart
        import 'package:googleapis_auth/auth_io.dart';
        import 'package:http/http.dart' as http;
        import 'package:url_launcher/url_launcher.dart';

        Future<AuthClient?> authenticate() async {
          var client = http.Client();
          try {
            // استبدل بمعرف العميل الخاص بك
            var clientId = ClientId("YOUR_CLIENT_ID.apps.googleusercontent.com", null);
            // النطاقات المطلوبة
            var scopes = [DriveApi.driveFileScope]; // أو DriveApi.driveScope

            // الحصول على بيانات الاعتماد عبر موافقة المستخدم
            AccessCredentials credentials = await obtainAccessCredentialsViaUserConsent(
              clientId,
              scopes,
              client,
              (String url) async {
                // فتح رابط المصادقة في المتصفح
                if (await canLaunchUrl(Uri.parse(url))) {
                  await launchUrl(Uri.parse(url));
                } else {
                  throw 'Could not launch $url';
                }
              },
              // قد تحتاج لمنفذ للاستماع للـ redirect URI إذا لم تستخدم loopback
              // port: 8080, 
            );

            // إنشاء عميل HTTP مصادق عليه
            return authenticatedClient(client, credentials);
          } catch (e) {
            print("خطأ في المصادقة: $e");
            client.close();
            return null;
          }
        }
        ```

2.  **اختيار الملف المراد رفعه:**
    *   استخدم `file_picker` أو `image_picker` للسماح للمستخدم باختيار ملف.
    ```dart
    import 'package:file_picker/file_picker.dart';
    import 'dart:io';

    Future<File?> pickFile() async {
      FilePickerResult? result = await FilePicker.platform.pickFiles();
      if (result != null) {
        return File(result.files.single.path!);
      } else {
        // المستخدم ألغى الاختيار
        return null;
      }
    }
    ```

3.  **استخدام Google Drive API لرفع الملف:**
    *   ستحتاج إلى `AuthClient` الذي حصلت عليه من خطوة المصادقة.
    *   قم بإنشاء كائن `DriveApi`.
    *   استخدم `drive.files.create` لرفع الملف.
    ```dart
    import 'package:googleapis/drive/v3.dart' as drive;
    import 'dart:io';
    import 'package:http/http.dart' as http;

    Future<void> uploadFile(AuthClient client, File fileToUpload) async {
      try {
        var driveApi = drive.DriveApi(client);
        var fileMetadata = drive.File();
        fileMetadata.name = fileToUpload.path.split('/').last; // اسم الملف
        // يمكنك تحديد مجلد الرفع باستخدام parents
        // fileMetadata.parents = ["FOLDER_ID"];

        // إنشاء طلب الرفع
        var response = await driveApi.files.create(
          fileMetadata,
          uploadMedia: drive.Media(fileToUpload.openRead(), fileToUpload.lengthSync()),
        );

        print("تم رفع الملف بنجاح! معرف الملف: ${response.id}");
      } catch (e) {
        print("خطأ في رفع الملف: $e");
        // التعامل مع الخطأ
      } finally {
        client.close(); // أغلق العميل عند الانتهاء
      }
    }
    ```

**ملاحظات هامة:**

*   **المصادقة هي الجزء الأصعب:** عملية الحصول على موافقة المستخدم وإدارة بيانات الاعتماد (Access Tokens, Refresh Tokens) تتطلب فهمًا جيدًا لـ OAuth 2.0. قد تحتاج إلى تخزين Refresh Token بشكل آمن لتجنب مطالبة المستخدم بالموافقة في كل مرة.
*   **الأمان:** لا تقم أبدًا بتضمين `Client Secret` مباشرة في كود التطبيق. بيانات اعتماد OAuth يجب التعامل معها بحذر.
*   **تجربة المستخدم:** عملية المصادقة يجب أن تكون سلسة وواضحة للمستخدم.
*   **الحدود والحصص:** Google Drive API لها حدود استخدام يومية. راجع وثائق Google Drive API.
*   **بدائل:** إذا كانت المصادقة معقدة للغاية، يمكنك التفكير في استخدام Firebase Storage لتخزين الملفات، فهو أسهل في التكامل مع Flutter و Firebase Authentication.

هذا الدليل يقدم خطوات أساسية. قد تحتاج إلى البحث بشكل أعمق في وثائق `googleapis`, `googleapis_auth` و Google Drive API لتطبيق تكامل كامل وقوي.
