rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد مجموعة المستخدمين
    match /users/{userId} {
      // السماح للمستخدم بقراءة وكتابة بياناته الخاصة فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // السماح بإنشاء مستند جديد للمستخدم المسجل الدخول
      allow create: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد مجموعة المنتدى المجتمعي
    match /community_posts/{postId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمؤلف فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.authorId == request.auth.uid);
      
      // السماح بإنشاء منشور جديد للمستخدمين المسجلين
      allow create: if request.auth != null && 
                    request.resource.data.authorId == request.auth.uid;
    }
    
    // قواعد مجموعة التعليقات
    match /community_posts/{postId}/comments/{commentId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمؤلف فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.authorId == request.auth.uid);
      
      // السماح بإنشاء تعليق جديد للمستخدمين المسجلين
      allow create: if request.auth != null && 
                    request.resource.data.authorId == request.auth.uid;
    }
    
    // قواعد مجموعة الاستشارات الزراعية
    match /agricultural_consultations/{consultationId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمؤلف فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.userId == request.auth.uid);
      
      // السماح بإنشاء استشارة جديدة للمستخدمين المسجلين
      allow create: if request.auth != null && 
                    request.resource.data.userId == request.auth.uid;
    }
    
    // قواعد مجموعة المرشدين الزراعيين
    match /agricultural_advisors/{advisorId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمرشد نفسه فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // قواعد مجموعة الردود على الاستشارات
    match /agricultural_consultations/{consultationId}/responses/{responseId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمرشد الزراعي فقط
      allow write: if request.auth != null && 
                   (resource == null || resource.data.advisorId == request.auth.uid);
      
      // السماح بإنشاء رد جديد للمرشدين الزراعيين
      allow create: if request.auth != null && 
                    request.resource.data.advisorId == request.auth.uid;
    }
    
    // قواعد مجموعة المحاصيل الزراعية (قراءة فقط)
    match /agricultural_crops/{cropId} {
      allow read: if true; // متاح للجميع
      allow write: if false; // لا يمكن التعديل من التطبيق
    }
    
    // قواعد مجموعة المعالم الزراعية (قراءة فقط)
    match /agricultural_landmarks/{landmarkId} {
      allow read: if true; // متاح للجميع
      allow write: if false; // لا يمكن التعديل من التطبيق
    }
    
    // قواعد مجموعة الآفات والأمراض (قراءة فقط)
    match /pests_and_diseases/{pestId} {
      allow read: if true; // متاح للجميع
      allow write: if false; // لا يمكن التعديل من التطبيق
    }
    
    // قواعد مجموعة الأخبار الزراعية (قراءة فقط)
    match /agricultural_news/{newsId} {
      allow read: if true; // متاح للجميع
      allow write: if false; // لا يمكن التعديل من التطبيق
    }
    
    // قواعد مجموعة بيانات الطقس (قراءة فقط)
    match /weather_data/{weatherId} {
      allow read: if true; // متاح للجميع
      allow write: if false; // لا يمكن التعديل من التطبيق
    }
    
    // قواعد افتراضية: رفض الوصول لأي مجموعة أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
